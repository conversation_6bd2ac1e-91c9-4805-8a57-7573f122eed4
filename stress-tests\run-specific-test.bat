@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    API Stress Testing - Specific Test Runner
echo ========================================
echo.

REM Check if k6 is installed
k6 version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: k6 is not installed or not in PATH
    echo Please install k6 from https://k6.io/docs/get-started/installation/
    pause
    exit /b 1
)

REM Create results directory if it doesn't exist
if not exist "results" mkdir results

REM Display menu
:menu
echo.
echo Select the test to run:
echo.
echo API Selection:
echo   1. Blur Detection API (port 8000)
echo   2. Image Inspector API (port 8080)
echo   3. Both APIs (sequential)
echo   4. Monitoring Test
echo   5. Baseline Performance Test
echo.
echo Scenario Selection:
echo   a. Light Load (10 users, 2 min)
echo   b. Moderate Load (50 users, 5 min)
echo   c. Heavy Load (100 users, 10 min)
echo   d. Spike Test (sudden load increase)
echo   e. Soak Test (25 users, 30 min)
echo   f. Stress Test (progressive load)
echo.
echo   0. Exit
echo.

set /p choice="Enter your choice (1-5 for API, then a-f for scenario): "

REM Parse choice
set api_choice=%choice:~0,1%
set scenario_choice=%choice:~1,1%

if "%choice%"=="0" goto :end
if "%choice%"=="4" goto :monitoring
if "%choice%"=="5" goto :baseline

REM Validate API choice
if "%api_choice%"=="1" set api_name=blur-detection
if "%api_choice%"=="2" set api_name=image-inspector
if "%api_choice%"=="3" set api_name=both
if not defined api_name (
    echo Invalid API choice. Please try again.
    goto :menu
)

REM Validate scenario choice
if "%scenario_choice%"=="a" set scenario=light_load
if "%scenario_choice%"=="b" set scenario=moderate_load
if "%scenario_choice%"=="c" set scenario=heavy_load
if "%scenario_choice%"=="d" set scenario=spike_test
if "%scenario_choice%"=="e" set scenario=soak_test
if "%scenario_choice%"=="f" set scenario=stress_test
if not defined scenario (
    echo Invalid scenario choice. Please try again.
    goto :menu
)

echo.
echo Selected: %api_name% API with %scenario% scenario
echo.

REM Check API availability
if "%api_name%"=="blur-detection" goto :check_blur
if "%api_name%"=="image-inspector" goto :check_inspector
if "%api_name%"=="both" goto :check_both

:check_blur
echo Checking Blur Detection API availability...
curl -s http://localhost:8000/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Blur Detection API not responding on port 8000
    echo Please start the API: cd blur-detection-api/blur-detection-api && python main.py
    pause
    goto :menu
)
echo ✓ Blur Detection API is running
goto :run_test

:check_inspector
echo Checking Image Inspector API availability...
curl -s http://localhost:8080/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Image Inspector API not responding on port 8080
    echo Please start the API: cd image-inspector-go-main && go run cmd/api/main.go
    pause
    goto :menu
)
echo ✓ Image Inspector API is running
goto :run_test

:check_both
echo Checking both APIs availability...
curl -s http://localhost:8000/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Blur Detection API not responding on port 8000
    echo Please start the API: cd blur-detection-api/blur-detection-api && python main.py
    pause
    goto :menu
)
echo ✓ Blur Detection API is running

curl -s http://localhost:8080/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Image Inspector API not responding on port 8080
    echo Please start the API: cd image-inspector-go-main && go run cmd/api/main.go
    pause
    goto :menu
)
echo ✓ Image Inspector API is running
goto :run_test

:run_test
echo.
echo Starting test at %date% %time%
echo.

if "%api_name%"=="blur-detection" (
    echo Running %scenario% test for Blur Detection API...
    k6 run -e SCENARIO=%scenario% scripts/blur-detection/load-test.js
) else if "%api_name%"=="image-inspector" (
    echo Running %scenario% test for Image Inspector API...
    k6 run -e SCENARIO=%scenario% scripts/image-inspector/load-test.js
) else if "%api_name%"=="both" (
    echo Running %scenario% test for Blur Detection API...
    k6 run -e SCENARIO=%scenario% scripts/blur-detection/load-test.js
    echo.
    echo Running %scenario% test for Image Inspector API...
    k6 run -e SCENARIO=%scenario% scripts/image-inspector/load-test.js
)

goto :test_complete

:monitoring
echo.
echo Starting continuous monitoring test...
echo Press Ctrl+C to stop monitoring
echo.
k6 run monitoring/performance-monitor.js
goto :test_complete

:baseline
echo.
echo Running baseline performance test...
echo.
k6 run scripts/utils/baseline.js
goto :test_complete

:test_complete
echo.
echo ========================================
echo    Test Complete
echo ========================================
echo.
echo Test completed at %date% %time%
echo.
echo Results are available in the 'results' directory.
echo.

REM Ask if user wants to run another test
set /p another="Run another test? (y/n): "
if /i "%another%"=="y" goto :menu

:end
echo.
echo Thank you for using the API Stress Testing Suite!
echo.
pause
