import http from 'k6/http';
import { check, sleep } from 'k6';
import { loadConfig, getRandomPayload, validateResponse } from './common.js';

// Baseline performance measurement script
export let options = {
  stages: [
    { duration: '30s', target: 1 }, // Single user baseline
  ],
  thresholds: {
    http_req_duration: ['p(95)<3000'], // 95% of requests under 3s
    http_req_failed: ['rate<0.01'],    // Less than 1% failures
  },
};

const BASE_URL_BLUR = 'http://localhost:8000';
const BASE_URL_INSPECTOR = 'http://localhost:8080';

export default function () {
  // Test Blur Detection API baseline
  testBlurDetectionBaseline();
  sleep(2);
  
  // Test Image Inspector API baseline
  testImageInspectorBaseline();
  sleep(2);
}

function testBlurDetectionBaseline() {
  console.log('=== Blur Detection API Baseline ===');
  
  // Health check
  let response = http.get(`${BASE_URL_BLUR}/health`);
  check(response, {
    'Blur API health check': (r) => r.status === 200,
  });
  
  // Root endpoint
  response = http.get(`${BASE_URL_BLUR}/`);
  check(response, {
    'Blur API root endpoint': (r) => r.status === 200,
  });
  
  // Thresholds endpoint
  response = http.get(`${BASE_URL_BLUR}/thresholds`);
  check(response, {
    'Blur API thresholds': (r) => r.status === 200,
  });
  
  // Main blur detection endpoint
  const blurPayload = getRandomPayload('blur_detection', 'basic');
  response = http.post(`${BASE_URL_BLUR}/detect-blur`, JSON.stringify(blurPayload), {
    headers: { 'Content-Type': 'application/json' },
  });
  
  const blurSuccess = check(response, {
    'Blur detection request': (r) => r.status === 200,
    'Blur detection response time': (r) => r.timings.duration < 5000,
    'Blur detection has result': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.laplacian_variance !== undefined && body.blur_level !== undefined;
      } catch (e) {
        return false;
      }
    },
  });
  
  if (blurSuccess) {
    const result = JSON.parse(response.body);
    console.log(`Blur Detection - Variance: ${result.laplacian_variance}, Level: ${result.blur_level}, Time: ${result.processing_time_ms}ms`);
  }
}

function testImageInspectorBaseline() {
  console.log('=== Image Inspector API Baseline ===');
  
  // Health check
  let response = http.get(`${BASE_URL_INSPECTOR}/health`);
  check(response, {
    'Inspector API health check': (r) => r.status === 200,
  });
  
  // Basic analysis
  const basicPayload = getRandomPayload('image_inspector', 'basic_analysis');
  response = http.post(`${BASE_URL_INSPECTOR}/analyze`, JSON.stringify(basicPayload), {
    headers: { 'Content-Type': 'application/json' },
  });
  
  const basicSuccess = check(response, {
    'Basic analysis request': (r) => r.status === 200,
    'Basic analysis response time': (r) => r.timings.duration < 10000,
    'Basic analysis has result': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.quality !== undefined && body.metrics !== undefined;
      } catch (e) {
        return false;
      }
    },
  });
  
  if (basicSuccess) {
    const result = JSON.parse(response.body);
    console.log(`Basic Analysis - Processing: ${result.processing_time_sec}s, Quality: ${JSON.stringify(result.quality)}`);
  }
  
  sleep(1);
  
  // Analysis with options
  const optionsPayload = getRandomPayload('image_inspector', 'analysis_with_options');
  response = http.post(`${BASE_URL_INSPECTOR}/analyze/options`, JSON.stringify(optionsPayload), {
    headers: { 'Content-Type': 'application/json' },
  });
  
  check(response, {
    'Options analysis request': (r) => r.status === 200,
    'Options analysis response time': (r) => r.timings.duration < 15000,
  });
  
  sleep(1);
  
  // Detailed analysis
  const detailedPayload = getRandomPayload('image_inspector', 'detailed_analysis');
  response = http.post(`${BASE_URL_INSPECTOR}/detailed-analyze`, JSON.stringify(detailedPayload), {
    headers: { 'Content-Type': 'application/json' },
  });
  
  const detailedSuccess = check(response, {
    'Detailed analysis request': (r) => r.status === 200,
    'Detailed analysis response time': (r) => r.timings.duration < 20000,
    'Detailed analysis comprehensive': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.quality_analysis !== undefined && body.raw_metrics !== undefined;
      } catch (e) {
        return false;
      }
    },
  });
  
  if (detailedSuccess) {
    const result = JSON.parse(response.body);
    console.log(`Detailed Analysis - Processing: ${result.processing_time_sec}s, Features: ${Object.keys(result.quality_analysis).length}`);
  }
}

export function handleSummary(data) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  
  return {
    [`../../results/baseline-${timestamp}.json`]: JSON.stringify(data, null, 2),
    [`../../results/baseline-${timestamp}.html`]: htmlReport(data),
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
  };
}

function htmlReport(data) {
  return `
<!DOCTYPE html>
<html>
<head>
    <title>Baseline Performance Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .metric { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 5px; }
        .success { background: #d4edda; }
        .warning { background: #fff3cd; }
        .error { background: #f8d7da; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Baseline Performance Report</h1>
    <p>Generated: ${new Date().toISOString()}</p>
    
    <h2>Summary</h2>
    <div class="metric">
        <strong>Total Requests:</strong> ${data.metrics.http_reqs?.values?.count || 0}
    </div>
    <div class="metric">
        <strong>Failed Requests:</strong> ${data.metrics.http_req_failed?.values?.rate || 0}
    </div>
    <div class="metric">
        <strong>Average Response Time:</strong> ${data.metrics.http_req_duration?.values?.avg?.toFixed(2) || 0}ms
    </div>
    <div class="metric">
        <strong>95th Percentile:</strong> ${data.metrics.http_req_duration?.values?.['p(95)']?.toFixed(2) || 0}ms
    </div>
    
    <h2>Detailed Metrics</h2>
    <table>
        <tr><th>Metric</th><th>Value</th></tr>
        ${Object.entries(data.metrics).map(([key, value]) => 
          `<tr><td>${key}</td><td>${JSON.stringify(value.values)}</td></tr>`
        ).join('')}
    </table>
</body>
</html>`;
}

function textSummary(data, options) {
  return `
=== BASELINE PERFORMANCE SUMMARY ===
Total Requests: ${data.metrics.http_reqs?.values?.count || 0}
Failed Requests: ${data.metrics.http_req_failed?.values?.rate || 0}
Average Response Time: ${data.metrics.http_req_duration?.values?.avg?.toFixed(2) || 0}ms
95th Percentile: ${data.metrics.http_req_duration?.values?.['p(95)']?.toFixed(2) || 0}ms
Max Response Time: ${data.metrics.http_req_duration?.values?.max?.toFixed(2) || 0}ms

Use this baseline to compare against load test results.
`;
}
