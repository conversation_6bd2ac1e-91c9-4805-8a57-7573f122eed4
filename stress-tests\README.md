# API Stress Testing Suite

This directory contains comprehensive stress testing tools for both APIs in the application:
- **Blur Detection API** (FastAPI on port 8000)
- **Image Inspector API** (Go/Gin on port 8080)

## Quick Start

1. **Install k6** (if not already installed):
   ```bash
   # Windows (using Chocolatey)
   choco install k6
   
   # Or download from https://k6.io/docs/get-started/installation/
   ```

2. **Start both APIs**:
   ```bash
   # Terminal 1: Start Blur Detection API
   cd blur-detection-api/blur-detection-api
   python main.py
   
   # Terminal 2: Start Image Inspector API
   cd image-inspector-go-main
   go run cmd/api/main.go
   ```

3. **Run stress tests**:
   ```bash
   cd stress-tests
   
   # Run all tests
   ./run-all-tests.bat
   
   # Or run individual tests
   k6 run scripts/blur-detection/load-test.js
   k6 run scripts/image-inspector/load-test.js
   ```

## Directory Structure

```
stress-tests/
├── README.md                    # This file
├── config/                      # Test configurations
│   ├── test-scenarios.json      # Load scenarios definition
│   └── test-images.json         # Test image URLs
├── scripts/                     # k6 test scripts
│   ├── blur-detection/          # Blur Detection API tests
│   ├── image-inspector/         # Image Inspector API tests
│   └── utils/                   # Shared utilities
├── results/                     # Test results and reports
├── monitoring/                  # Performance monitoring tools
└── run-all-tests.bat           # Execute all tests
```

## Test Scenarios

### Load Levels
- **Light Load**: 10 concurrent users, 2 minutes
- **Moderate Load**: 50 concurrent users, 5 minutes  
- **Heavy Load**: 100 concurrent users, 10 minutes
- **Spike Test**: Sudden load increase to 200 users
- **Soak Test**: 25 users for 30 minutes (endurance)

### Test Types
1. **Load Testing**: Normal expected load
2. **Stress Testing**: Beyond normal capacity
3. **Spike Testing**: Sudden traffic increases
4. **Volume Testing**: Large amounts of data
5. **Endurance Testing**: Extended periods

## Key Metrics Monitored

- **Response Time**: Average, 95th percentile, max
- **Throughput**: Requests per second
- **Error Rate**: Failed requests percentage
- **Resource Usage**: CPU, memory, network
- **Concurrent Users**: Active user simulation

## Getting Started

See individual test directories for specific instructions:
- [Blur Detection Tests](scripts/blur-detection/README.md)
- [Image Inspector Tests](scripts/image-inspector/README.md)

## Installation and Setup

### Prerequisites
1. **k6 Installation**: Download and install k6 from [k6.io](https://k6.io/docs/get-started/installation/)
   ```bash
   # Windows (using Chocolatey)
   choco install k6

   # Windows (using Scoop)
   scoop install k6

   # Or download binary from GitHub releases
   ```

2. **Node.js** (for dashboard generation): Install Node.js 16+ from [nodejs.org](https://nodejs.org/)

3. **APIs Running**: Ensure both APIs are running:
   - Blur Detection API on port 8000
   - Image Inspector API on port 8080

### Quick Setup
```bash
# Clone or navigate to the stress-tests directory
cd stress-tests

# Verify k6 installation
k6 version

# Check API availability
curl http://localhost:8000/health
curl http://localhost:8080/health

# Run all tests
./run-all-tests.bat

# Or run specific tests
./run-specific-test.bat
```

## Test Execution Options

### 1. Automated Test Suite
Run all tests with comprehensive reporting:
```bash
./run-all-tests.bat
```

### 2. Interactive Test Selection
Choose specific tests and scenarios:
```bash
./run-specific-test.bat
```

### 3. Manual Test Execution
Run individual tests with custom parameters:
```bash
# Basic load test
k6 run scripts/blur-detection/load-test.js

# Specific scenario
k6 run -e SCENARIO=heavy_load scripts/image-inspector/load-test.js

# Custom VU count and duration
k6 run --vus 75 --duration 8m scripts/blur-detection/load-test.js

# Spike testing
k6 run scripts/blur-detection/spike-test.js
k6 run scripts/image-inspector/spike-test.js
```

### 4. Continuous Monitoring
Long-running performance monitoring:
```bash
k6 run monitoring/performance-monitor.js
```

## Understanding Test Results

### Success Criteria
- ✅ **Error Rate**: < 5% for normal load, < 15% for spike tests
- ✅ **Response Time**: 95th percentile under defined thresholds
- ✅ **Availability**: Health checks always succeed
- ✅ **Recovery**: Fast recovery after spike tests

### Performance Thresholds
| API | Endpoint | 95th Percentile | Max Acceptable |
|-----|----------|----------------|----------------|
| Blur Detection | /detect-blur | < 3000ms | < 5000ms |
| Image Inspector | /analyze | < 8000ms | < 10000ms |
| Image Inspector | /analyze/options | < 12000ms | < 15000ms |
| Image Inspector | /detailed-analyze | < 15000ms | < 20000ms |

### Report Types
1. **JSON Reports**: Raw metrics data for analysis
2. **HTML Reports**: Visual dashboards with charts
3. **CSV Reports**: Time-series data for trending
4. **Console Output**: Real-time progress and summary

## Troubleshooting Guide

### Common Issues

#### 1. Connection Refused Errors
```
ERRO[0001] Get "http://localhost:8000/health": dial tcp [::1]:8000: connectex: No connection could be made
```
**Solution**: Ensure the API is running on the correct port
```bash
# For Blur Detection API
cd blur-detection-api/blur-detection-api
python main.py

# For Image Inspector API
cd image-inspector-go-main
go run cmd/api/main.go
```

#### 2. High Error Rates
**Symptoms**: Error rate > 10% during normal load tests
**Possible Causes**:
- API server overloaded
- Network connectivity issues
- Invalid test data URLs
- API configuration problems

**Solutions**:
- Check API server logs
- Reduce concurrent users
- Verify test image URLs are accessible
- Monitor server resources (CPU, memory)

#### 3. Slow Response Times
**Symptoms**: Response times consistently above thresholds
**Possible Causes**:
- Server resource constraints
- Large image processing
- Network latency
- Database bottlenecks

**Solutions**:
- Monitor server performance
- Optimize image sizes in test data
- Check network connectivity
- Review API configuration

#### 4. Test Script Errors
**Symptoms**: k6 script execution failures
**Common Issues**:
- Missing dependencies
- Invalid JSON in config files
- File path issues

**Solutions**:
```bash
# Verify k6 installation
k6 version

# Check file paths and permissions
ls -la scripts/
ls -la config/

# Validate JSON configuration
node -e "console.log(JSON.parse(require('fs').readFileSync('config/test-scenarios.json')))"
```

### Debug Mode
Run tests with verbose logging for troubleshooting:
```bash
# Verbose output
k6 run --verbose scripts/blur-detection/load-test.js

# Single iteration for debugging
k6 run --iterations 1 scripts/blur-detection/load-test.js

# HTTP debug logging
k6 run --http-debug scripts/blur-detection/load-test.js
```

### Performance Optimization Tips

#### For APIs
1. **Connection Pooling**: Configure HTTP client connection pools
2. **Caching**: Implement response caching for repeated requests
3. **Resource Limits**: Set appropriate CPU/memory limits
4. **Timeouts**: Configure reasonable request timeouts
5. **Load Balancing**: Use multiple API instances for high load

#### For Tests
1. **Realistic Data**: Use representative test images
2. **Gradual Ramp-up**: Avoid sudden load increases
3. **Think Time**: Include realistic user delays
4. **Batch Requests**: Group related requests efficiently
5. **Resource Monitoring**: Monitor test runner resources

## Advanced Usage

### Custom Test Scenarios
Create custom test scenarios by modifying `config/test-scenarios.json`:
```json
{
  "scenarios": {
    "custom_scenario": {
      "name": "Custom Load Test",
      "virtual_users": 25,
      "duration": "3m",
      "ramp_up": "45s",
      "ramp_down": "45s"
    }
  }
}
```

### Environment Variables
Customize test behavior with environment variables:
```bash
# Custom scenario
k6 run -e SCENARIO=custom_scenario scripts/blur-detection/load-test.js

# Custom API URLs
k6 run -e BLUR_API_URL=http://api.example.com:8000 scripts/blur-detection/load-test.js

# Debug mode
k6 run -e DEBUG=true scripts/blur-detection/load-test.js
```

### Integration with CI/CD
Example GitHub Actions workflow:
```yaml
name: API Stress Tests
on: [push, pull_request]
jobs:
  stress-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Install k6
        run: |
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
      - name: Start APIs
        run: |
          # Start your APIs here
      - name: Run stress tests
        run: |
          cd stress-tests
          k6 run scripts/blur-detection/load-test.js
          k6 run scripts/image-inspector/load-test.js
```

## Monitoring and Alerting

### Real-time Monitoring
Use the performance monitor for continuous monitoring:
```bash
# Start continuous monitoring
k6 run monitoring/performance-monitor.js

# Generate dashboard every 5 minutes
while true; do
  node monitoring/dashboard-generator.js
  sleep 300
done
```

### Alerting Setup
Set up alerts based on test results:
1. **Error Rate Alerts**: > 5% error rate
2. **Response Time Alerts**: 95th percentile above thresholds
3. **Availability Alerts**: Health check failures
4. **Resource Alerts**: High CPU/memory usage

### Metrics Export
Export metrics to external monitoring systems:
```bash
# Export to InfluxDB
k6 run --out influxdb=http://localhost:8086/k6 scripts/blur-detection/load-test.js

# Export to Prometheus
k6 run --out prometheus scripts/blur-detection/load-test.js

# Export to JSON
k6 run --out json=results.json scripts/blur-detection/load-test.js
```
