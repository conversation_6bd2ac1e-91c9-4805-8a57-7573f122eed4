# Image Inspector API Stress Tests

This directory contains comprehensive stress tests for the Go Image Inspector service running on port 8080.

## Test Scripts

### 1. `load-test.js` - Main Load Testing Script
Comprehensive load testing with multiple scenarios and realistic user behavior simulation.

**Features:**
- Multiple test scenarios with weighted selection
- All three endpoints coverage (/analyze, /analyze/options, /detailed-analyze)
- OCR analysis testing
- Batch processing simulation
- Error handling validation
- Custom metrics for each endpoint

**Usage:**
```bash
# Run with default moderate load scenario
k6 run load-test.js

# Run with specific scenario
k6 run -e SCENARIO=light_load load-test.js
k6 run -e SCENARIO=heavy_load load-test.js
k6 run -e SCENARIO=spike_test load-test.js
```

## Available Scenarios

- **light_load**: 10 users, 2 minutes
- **moderate_load**: 50 users, 5 minutes  
- **heavy_load**: 100 users, 10 minutes
- **spike_test**: Sudden spike to 200 users
- **soak_test**: 25 users for 30 minutes
- **stress_test**: Progressive load increase

## Test Endpoints Covered

1. **GET /health** - Health check endpoint
2. **POST /analyze** - Basic image analysis
3. **POST /analyze/options** - Analysis with custom options
4. **POST /detailed-analyze** - Comprehensive detailed analysis

## Test Scenarios

### Weighted Test Distribution
The load test uses realistic usage patterns:
- **Health checks**: 10% (monitoring/uptime checks)
- **Basic analysis**: 30% (most common usage)
- **Analysis with options**: 20% (advanced users)
- **Detailed analysis**: 20% (comprehensive reports)
- **OCR analysis**: 10% (text processing)
- **Batch processing**: 5% (bulk operations)
- **Error handling**: 5% (edge cases)

### Payload Variations

**Basic Analysis:**
- Standard image analysis with optional OCR
- Various image sizes and types
- OCR quality validation with expected text

**Analysis with Options:**
- Custom quality thresholds
- Feature toggles (QR detection, white balance, etc.)
- Performance optimizations (worker pools, fast mode)

**Detailed Analysis:**
- Comprehensive analysis mode
- Performance metrics inclusion
- Raw metrics and thresholds
- Custom feature flags

## Metrics Tracked

### Standard HTTP Metrics
- Response time (avg, min, max, percentiles)
- Request rate (requests per second)
- Error rate (failed requests percentage)
- Data transfer rates

### Custom Image Inspector Metrics
- `inspector_duration`: Time for inspector requests
- `inspector_errors`: Error rate for inspector operations
- `inspector_requests`: Count of inspector requests
- `analysis_processing_time`: Server-side processing time
- `analyze_endpoint_duration`: /analyze endpoint performance
- `analyze_options_endpoint_duration`: /analyze/options performance
- `detailed_analyze_endpoint_duration`: /detailed-analyze performance

## Performance Thresholds

- **Response Time**: 95% of requests under 10 seconds
- **Error Rate**: Less than 5% failures
- **Processing Time**: 90% of analysis under 8 seconds
- **Endpoint Specific**:
  - `/analyze`: 95% under 8 seconds
  - `/analyze/options`: 95% under 12 seconds
  - `/detailed-analyze`: 95% under 15 seconds

## Running Tests

### Prerequisites
1. Ensure Image Inspector API is running on port 8080
2. Install k6: `choco install k6` (Windows) or download from k6.io

### Basic Test Execution
```bash
# Navigate to image inspector test directory
cd stress-tests/scripts/image-inspector

# Run baseline performance test first
k6 run ../utils/baseline.js

# Run load test with moderate load
k6 run load-test.js

# Run with custom scenario
k6 run -e SCENARIO=heavy_load load-test.js
```

### Advanced Options
```bash
# Run with custom VU count and duration
k6 run --vus 75 --duration 8m load-test.js

# Run with specific stages
k6 run --stage 1m:10,5m:50,1m:0 load-test.js

# Output results to specific file
k6 run --out json=results.json load-test.js
```

## Interpreting Results

### Success Criteria
- ✅ Error rate < 5%
- ✅ 95th percentile response time < 10000ms
- ✅ All health checks pass
- ✅ Endpoint-specific thresholds met
- ✅ No memory leaks in soak tests

### Warning Signs
- ⚠️ Error rate 5-10%
- ⚠️ Response time degradation over time
- ⚠️ High variance in processing times
- ⚠️ Timeouts on detailed analysis
- ⚠️ OCR analysis failures

### Critical Issues
- ❌ Error rate > 10%
- ❌ Response times > 20 seconds
- ❌ Service unavailability
- ❌ Memory leaks or crashes
- ❌ Consistent timeouts

## Troubleshooting

### Common Issues
1. **Connection refused**: Ensure API is running on port 8080
2. **High error rates**: Check API logs for specific errors
3. **Slow responses**: Monitor CPU/memory usage on API server
4. **Timeouts**: Increase timeout values for detailed analysis
5. **OCR failures**: Verify text image URLs are accessible

### Debug Mode
```bash
# Run with verbose logging
k6 run --verbose load-test.js

# Run single iteration for debugging
k6 run --iterations 1 load-test.js

# Test specific endpoint
k6 run -e SCENARIO=light_load load-test.js
```

## Report Generation

Tests automatically generate:
- **JSON reports**: Detailed metrics in `../../results/`
- **HTML reports**: Visual dashboards with endpoint breakdowns
- **Console output**: Real-time progress and summary

Example report files:
- `image-inspector-moderate-load-2024-01-15T10-30-00.json`
- `image-inspector-moderate-load-2024-01-15T10-30-00.html`

## Performance Optimization Tips

Based on test results, consider:
1. **Caching**: Implement response caching for repeated requests
2. **Connection pooling**: Optimize HTTP client connections
3. **Resource limits**: Adjust Go runtime settings (GOMAXPROCS)
4. **Timeouts**: Fine-tune request/analysis timeouts
5. **Worker pools**: Optimize concurrent processing settings
