import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { 
  getRandomPayload, 
  validateResponse, 
  simulateUserThinkTime, 
  generateTestOptions,
  withRetry,
  logPerformanceMetrics 
} from '../utils/common.js';

// Custom metrics for Image Inspector API
const inspectorErrors = new Rate('inspector_errors');
const inspectorDuration = new Trend('inspector_duration');
const inspectorRequests = new Counter('inspector_requests');
const analysisProcessingTime = new Trend('analysis_processing_time');
const endpointMetrics = {
  analyze: new Trend('analyze_endpoint_duration'),
  analyzeOptions: new Trend('analyze_options_endpoint_duration'),
  detailedAnalyze: new Trend('detailed_analyze_endpoint_duration')
};

// Configuration
const BASE_URL = 'http://localhost:8080';
const SCENARIO = __ENV.SCENARIO || 'moderate_load';

// Test options based on scenario
export let options = generateTestOptions(SCENARIO);

// Add specific thresholds for image inspector
options.thresholds = {
  ...options.thresholds,
  'inspector_errors': ['rate<0.05'], // Less than 5% errors
  'inspector_duration': ['p(95)<10000'], // 95% under 10 seconds
  'analysis_processing_time': ['p(90)<8000'], // 90% processing under 8 seconds
  'analyze_endpoint_duration': ['p(95)<8000'],
  'analyze_options_endpoint_duration': ['p(95)<12000'],
  'detailed_analyze_endpoint_duration': ['p(95)<15000'],
};

export default function () {
  const testScenarios = [
    testHealthEndpoint,
    testBasicAnalysis,
    testAnalysisWithOptions,
    testDetailedAnalysis,
    testOCRAnalysis,
    testBatchAnalysis,
    testErrorHandling
  ];
  
  // Weighted random selection to simulate realistic usage patterns
  const weights = [0.1, 0.3, 0.2, 0.2, 0.1, 0.05, 0.05];
  const scenario = selectWeightedRandom(testScenarios, weights);
  scenario();
  
  simulateUserThinkTime(1, 5);
}

function selectWeightedRandom(items, weights) {
  const random = Math.random();
  let weightSum = 0;
  
  for (let i = 0; i < items.length; i++) {
    weightSum += weights[i];
    if (random <= weightSum) {
      return items[i];
    }
  }
  
  return items[items.length - 1];
}

function testHealthEndpoint() {
  const response = http.get(`${BASE_URL}/health`);
  validateResponse(response, 200, 'Health Check');
}

function testBasicAnalysis() {
  const payload = getRandomPayload('image_inspector', 'basic_analysis');
  
  const response = withRetry(() => 
    http.post(`${BASE_URL}/analyze`, JSON.stringify(payload), {
      headers: { 'Content-Type': 'application/json' },
      timeout: '30s',
    })
  );
  
  const isSuccess = check(response, {
    'Basic analysis status 200': (r) => r.status === 200,
    'Basic analysis response time < 10s': (r) => r.timings.duration < 10000,
    'Basic analysis has quality': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.quality !== undefined;
      } catch (e) {
        return false;
      }
    },
    'Basic analysis has metrics': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.metrics !== undefined;
      } catch (e) {
        return false;
      }
    },
    'Basic analysis has processing time': (r) => {
      try {
        const body = JSON.parse(r.body);
        return typeof body.processing_time_sec === 'number' && body.processing_time_sec > 0;
      } catch (e) {
        return false;
      }
    },
  });
  
  // Record custom metrics
  inspectorErrors.add(!isSuccess);
  inspectorDuration.add(response.timings.duration);
  inspectorRequests.add(1);
  endpointMetrics.analyze.add(response.timings.duration);
  
  if (isSuccess && response.body) {
    try {
      const result = JSON.parse(response.body);
      analysisProcessingTime.add(result.processing_time_sec * 1000);
      logPerformanceMetrics(response, `basic_analysis_${result.quality?.blurry ? 'blurry' : 'sharp'}`);
    } catch (e) {
      console.error('Failed to parse basic analysis response:', e);
    }
  }
}

function testAnalysisWithOptions() {
  const payload = getRandomPayload('image_inspector', 'analysis_with_options');
  
  const response = http.post(`${BASE_URL}/analyze/options`, JSON.stringify(payload), {
    headers: { 'Content-Type': 'application/json' },
    timeout: '45s',
  });
  
  const isSuccess = check(response, {
    'Options analysis status 200': (r) => r.status === 200,
    'Options analysis response time < 15s': (r) => r.timings.duration < 15000,
    'Options analysis has quality': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.quality !== undefined;
      } catch (e) {
        return false;
      }
    },
    'Options analysis respects options': (r) => {
      try {
        const body = JSON.parse(r.body);
        // Check if quality_mode affected the response
        return body.metrics !== undefined;
      } catch (e) {
        return false;
      }
    },
  });
  
  inspectorErrors.add(!isSuccess);
  inspectorRequests.add(1);
  endpointMetrics.analyzeOptions.add(response.timings.duration);
  
  if (isSuccess && response.body) {
    try {
      const result = JSON.parse(response.body);
      console.log(`Options Analysis - Processing: ${result.processing_time_sec}s, Quality Mode: ${payload.options.quality_mode}`);
    } catch (e) {
      console.error('Failed to parse options analysis response:', e);
    }
  }
}

function testDetailedAnalysis() {
  const payload = getRandomPayload('image_inspector', 'detailed_analysis');
  
  const response = http.post(`${BASE_URL}/detailed-analyze`, JSON.stringify(payload), {
    headers: { 'Content-Type': 'application/json' },
    timeout: '60s', // Longer timeout for detailed analysis
  });
  
  const isSuccess = check(response, {
    'Detailed analysis status 200': (r) => r.status === 200,
    'Detailed analysis response time < 20s': (r) => r.timings.duration < 20000,
    'Detailed analysis has quality_analysis': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.quality_analysis !== undefined;
      } catch (e) {
        return false;
      }
    },
    'Detailed analysis has raw_metrics': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.raw_metrics !== undefined;
      } catch (e) {
        return false;
      }
    },
    'Detailed analysis has thresholds': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.applied_thresholds !== undefined;
      } catch (e) {
        return false;
      }
    },
    'Detailed analysis comprehensive': (r) => {
      try {
        const body = JSON.parse(r.body);
        return Object.keys(body.quality_analysis || {}).length > 5;
      } catch (e) {
        return false;
      }
    },
  });
  
  inspectorErrors.add(!isSuccess);
  inspectorRequests.add(1);
  endpointMetrics.detailedAnalyze.add(response.timings.duration);
  
  if (isSuccess && response.body) {
    try {
      const result = JSON.parse(response.body);
      const featuresCount = Object.keys(result.quality_analysis || {}).length;
      console.log(`Detailed Analysis - Processing: ${result.processing_time_sec}s, Features: ${featuresCount}`);
    } catch (e) {
      console.error('Failed to parse detailed analysis response:', e);
    }
  }
}

function testOCRAnalysis() {
  const payload = getRandomPayload('image_inspector', 'ocr_analysis');
  
  const response = http.post(`${BASE_URL}/analyze`, JSON.stringify(payload), {
    headers: { 'Content-Type': 'application/json' },
    timeout: '45s',
  });
  
  const isSuccess = check(response, {
    'OCR analysis status 200': (r) => r.status === 200,
    'OCR analysis response time < 15s': (r) => r.timings.duration < 15000,
    'OCR analysis has ocr_result': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.ocr_result !== undefined;
      } catch (e) {
        return false;
      }
    },
  });
  
  inspectorErrors.add(!isSuccess);
  inspectorRequests.add(1);
  
  if (isSuccess && response.body) {
    try {
      const result = JSON.parse(response.body);
      const hasOCR = result.ocr_result !== null;
      console.log(`OCR Analysis - Processing: ${result.processing_time_sec}s, OCR Result: ${hasOCR}`);
    } catch (e) {
      console.error('Failed to parse OCR analysis response:', e);
    }
  }
}

function testBatchAnalysis() {
  // Simulate batch processing with multiple concurrent requests
  const batchSize = Math.floor(Math.random() * 3) + 2; // 2-4 requests
  const requests = [];
  
  for (let i = 0; i < batchSize; i++) {
    const endpointType = Math.random();
    let endpoint, payload;
    
    if (endpointType < 0.5) {
      endpoint = '/analyze';
      payload = getRandomPayload('image_inspector', 'basic_analysis');
    } else if (endpointType < 0.8) {
      endpoint = '/analyze/options';
      payload = getRandomPayload('image_inspector', 'analysis_with_options');
    } else {
      endpoint = '/detailed-analyze';
      payload = getRandomPayload('image_inspector', 'detailed_analysis');
    }
    
    requests.push([
      'POST',
      `${BASE_URL}${endpoint}`,
      JSON.stringify(payload),
      { headers: { 'Content-Type': 'application/json' } }
    ]);
  }
  
  const responses = http.batch(requests);
  
  responses.forEach((response, index) => {
    const isSuccess = check(response, {
      [`Batch request ${index + 1} status`]: (r) => r.status === 200,
      [`Batch request ${index + 1} response time`]: (r) => r.timings.duration < 25000,
    });
    
    inspectorErrors.add(!isSuccess);
    inspectorRequests.add(1);
  });
  
  console.log(`Batch processing: ${batchSize} requests completed`);
}

function testErrorHandling() {
  // Test with invalid image URL
  const invalidPayload = {
    url: "https://invalid-url-that-does-not-exist.com/image.jpg",
    is_ocr: false
  };
  
  let response = http.post(`${BASE_URL}/analyze`, JSON.stringify(invalidPayload), {
    headers: { 'Content-Type': 'application/json' },
    timeout: '20s',
  });
  
  check(response, {
    'Invalid URL handled gracefully': (r) => r.status >= 400 && r.status < 600,
    'Error response structured': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.errors !== undefined || body.error !== undefined;
      } catch (e) {
        return r.body.includes('error') || r.body.includes('failed');
      }
    },
  });
  
  sleep(1);
  
  // Test with malformed JSON
  response = http.post(`${BASE_URL}/analyze`, '{"invalid": json}', {
    headers: { 'Content-Type': 'application/json' },
  });
  
  check(response, {
    'Malformed JSON handled': (r) => r.status === 400 || r.status === 422,
  });
  
  sleep(1);
  
  // Test with missing required fields
  response = http.post(`${BASE_URL}/detailed-analyze`, '{}', {
    headers: { 'Content-Type': 'application/json' },
  });
  
  check(response, {
    'Missing fields validation': (r) => r.status === 400 || r.status === 422,
  });
}

export function handleSummary(data) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const scenarioName = SCENARIO.replace('_', '-');

  return {
    [`../../results/image-inspector-${scenarioName}-${timestamp}.json`]: JSON.stringify(data, null, 2),
    [`../../results/image-inspector-${scenarioName}-${timestamp}.html`]: htmlReport(data),
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
  };
}

function htmlReport(data) {
  const metrics = data.metrics;
  const timestamp = new Date().toISOString();

  return `
<!DOCTYPE html>
<html>
<head>
    <title>Image Inspector API - Load Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .header { text-align: center; color: #333; border-bottom: 2px solid #28a745; padding-bottom: 20px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; }
        .metric-title { font-weight: bold; color: #495057; margin-bottom: 10px; }
        .metric-value { font-size: 1.2em; color: #28a745; }
        .success { border-left-color: #28a745; }
        .warning { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #dee2e6; padding: 12px; text-align: left; }
        th { background-color: #e9ecef; font-weight: bold; }
        .summary { background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .endpoint-section { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Image Inspector API Load Test Report</h1>
            <p>Scenario: <strong>${SCENARIO}</strong> | Generated: ${timestamp}</p>
        </div>

        <div class="summary">
            <h2>📊 Test Summary</h2>
            <div class="metric-grid">
                <div class="metric-card ${(metrics.http_req_failed?.values?.rate || 0) < 0.05 ? 'success' : 'error'}">
                    <div class="metric-title">Success Rate</div>
                    <div class="metric-value">${((1 - (metrics.http_req_failed?.values?.rate || 0)) * 100).toFixed(2)}%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Total Requests</div>
                    <div class="metric-value">${metrics.http_reqs?.values?.count || 0}</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Avg Response Time</div>
                    <div class="metric-value">${(metrics.http_req_duration?.values?.avg || 0).toFixed(2)}ms</div>
                </div>
                <div class="metric-card ${(metrics.http_req_duration?.values?.['p(95)'] || 0) < 10000 ? 'success' : 'warning'}">
                    <div class="metric-title">95th Percentile</div>
                    <div class="metric-value">${(metrics.http_req_duration?.values?.['p(95)'] || 0).toFixed(2)}ms</div>
                </div>
            </div>
        </div>

        <div class="endpoint-section">
            <h2>🎯 Endpoint Performance</h2>
            <div class="metric-grid">
                <div class="metric-card">
                    <div class="metric-title">/analyze Avg Time</div>
                    <div class="metric-value">${(metrics.analyze_endpoint_duration?.values?.avg || 0).toFixed(2)}ms</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">/analyze/options Avg Time</div>
                    <div class="metric-value">${(metrics.analyze_options_endpoint_duration?.values?.avg || 0).toFixed(2)}ms</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">/detailed-analyze Avg Time</div>
                    <div class="metric-value">${(metrics.detailed_analyze_endpoint_duration?.values?.avg || 0).toFixed(2)}ms</div>
                </div>
            </div>
        </div>

        <h2>📈 Performance Metrics</h2>
        <table>
            <tr><th>Metric</th><th>Average</th><th>Min</th><th>Max</th><th>90th %ile</th><th>95th %ile</th></tr>
            <tr>
                <td>HTTP Request Duration</td>
                <td>${(metrics.http_req_duration?.values?.avg || 0).toFixed(2)}ms</td>
                <td>${(metrics.http_req_duration?.values?.min || 0).toFixed(2)}ms</td>
                <td>${(metrics.http_req_duration?.values?.max || 0).toFixed(2)}ms</td>
                <td>${(metrics.http_req_duration?.values?.['p(90)'] || 0).toFixed(2)}ms</td>
                <td>${(metrics.http_req_duration?.values?.['p(95)'] || 0).toFixed(2)}ms</td>
            </tr>
            <tr>
                <td>Inspector Duration</td>
                <td>${(metrics.inspector_duration?.values?.avg || 0).toFixed(2)}ms</td>
                <td>${(metrics.inspector_duration?.values?.min || 0).toFixed(2)}ms</td>
                <td>${(metrics.inspector_duration?.values?.max || 0).toFixed(2)}ms</td>
                <td>${(metrics.inspector_duration?.values?.['p(90)'] || 0).toFixed(2)}ms</td>
                <td>${(metrics.inspector_duration?.values?.['p(95)'] || 0).toFixed(2)}ms</td>
            </tr>
            <tr>
                <td>Analysis Processing Time</td>
                <td>${(metrics.analysis_processing_time?.values?.avg || 0).toFixed(2)}ms</td>
                <td>${(metrics.analysis_processing_time?.values?.min || 0).toFixed(2)}ms</td>
                <td>${(metrics.analysis_processing_time?.values?.max || 0).toFixed(2)}ms</td>
                <td>${(metrics.analysis_processing_time?.values?.['p(90)'] || 0).toFixed(2)}ms</td>
                <td>${(metrics.analysis_processing_time?.values?.['p(95)'] || 0).toFixed(2)}ms</td>
            </tr>
        </table>

        <h2>📊 Custom Metrics</h2>
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-title">Inspector Requests</div>
                <div class="metric-value">${metrics.inspector_requests?.values?.count || 0}</div>
            </div>
            <div class="metric-card ${(metrics.inspector_errors?.values?.rate || 0) < 0.05 ? 'success' : 'error'}">
                <div class="metric-title">Inspector Error Rate</div>
                <div class="metric-value">${((metrics.inspector_errors?.values?.rate || 0) * 100).toFixed(2)}%</div>
            </div>
        </div>
    </div>
</body>
</html>`;
}

function textSummary(data, options) {
  const metrics = data.metrics;
  return `
=== IMAGE INSPECTOR API LOAD TEST SUMMARY ===
Scenario: ${SCENARIO}
Total Requests: ${metrics.http_reqs?.values?.count || 0}
Failed Requests: ${((metrics.http_req_failed?.values?.rate || 0) * 100).toFixed(2)}%
Average Response Time: ${(metrics.http_req_duration?.values?.avg || 0).toFixed(2)}ms
95th Percentile: ${(metrics.http_req_duration?.values?.['p(95)'] || 0).toFixed(2)}ms
Max Response Time: ${(metrics.http_req_duration?.values?.max || 0).toFixed(2)}ms

Endpoint Performance:
- /analyze: ${(metrics.analyze_endpoint_duration?.values?.avg || 0).toFixed(2)}ms avg
- /analyze/options: ${(metrics.analyze_options_endpoint_duration?.values?.avg || 0).toFixed(2)}ms avg
- /detailed-analyze: ${(metrics.detailed_analyze_endpoint_duration?.values?.avg || 0).toFixed(2)}ms avg

Inspector Specific:
- Inspector Requests: ${metrics.inspector_requests?.values?.count || 0}
- Inspector Error Rate: ${((metrics.inspector_errors?.values?.rate || 0) * 100).toFixed(2)}%
- Analysis Processing Time: ${(metrics.analysis_processing_time?.values?.avg || 0).toFixed(2)}ms avg
`;
}
