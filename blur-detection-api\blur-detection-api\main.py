from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from models import ImageRequest, BlurDetectionResponse
from blur_detector import BlurDetector
import asyncio

app = FastAPI(
    title="Blur Detection API",
    description="Fast blur detection using Laplacian Variance method with OpenCV",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize blur detector
blur_detector = BlurDetector()

@app.get("/")
async def root():
    return {
        "message": "Blur Detection API",
        "version": "1.0.0",
        "endpoints": {
            "detect_blur": "/detect-blur",
            "health": "/health"
        }
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "blur-detection-api"}

@app.post("/detect-blur", response_model=BlurDetectionResponse)
async def detect_blur(request: ImageRequest):
    """
    Detect blur and image quality issues from URL using computer vision methods.
    
    - **image_url**: URL of the image to analyze
    - **max_resolution**: Maximum width/height for downscaling (default: 1024)
    
    Returns detailed analysis including:
    - Blur detection (Laplacian variance method)
    - Overexposure detection (histogram analysis)
    - Oversaturation detection (HSV color space analysis)
    - White balance issues (color cast detection)
    - Processing time and image metadata
    - Any errors or warnings
    """
    try:
        result = await blur_detector.detect_blur(
            str(request.image_url), 
            request.max_resolution
        )
        
        return BlurDetectionResponse(**result)
        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Blur detection failed: {str(e)}"
        )

@app.get("/thresholds")
async def get_thresholds():
    """Get current blur classification thresholds"""
    return {
        "thresholds": blur_detector.thresholds,
        "description": {
            "sharp": "Laplacian variance >= 500 (clear, focused image)",
            "slightly_blurred": "200 <= variance < 500 (minor blur)",
            "moderately_blurred": "50 <= variance < 200 (noticeable blur)",
            "heavily_blurred": "variance < 50 (severely blurred)"
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app", 
        host="0.0.0.0", 
        port=8000, 
        reload=True,
        log_level="info"
    )
