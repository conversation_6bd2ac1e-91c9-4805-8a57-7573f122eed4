# Blur Detection API

Fast blur detection using Laplacian Variance method with OpenCV. Provides sub-second response times for images from KB to 20MB+.

## Features

- **Fast Processing**: Sub-second response for all image sizes
- **Smart Downscaling**: Auto-downscales images > 1MB for speed
- **Grayscale Optimization**: Converts to grayscale for faster processing
- **Comprehensive Analysis**: Returns variance, classification, confidence, and metadata
- **Error Handling**: Detailed error reporting and warnings

## Installation

```bash
cd blur-detection-api
pip install -r requirements.txt
```

## Usage

### Start the API
```bash
python main.py
```

The API will be available at `http://localhost:8000`

### API Documentation
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Endpoints

### POST /detect-blur
Analyze image blur from URL.

**Request:**
```json
{
  "image_url": "https://example.com/image.jpg",
  "max_resolution": 1024
}
```

**Response:**
```json
{
  "laplacian_variance": 245.67,
  "blur_level": "slightly_blurred",
  "is_blurred": true,
  "confidence_score": 0.7,
  "processing_time_ms": 234.5,
  "image_info": {
    "original_size_bytes": 2048576,
    "original_dimensions": [1920, 1080],
    "processed_dimensions": [1024, 576],
    "was_downscaled": true,
    "format": "JPEG",
    "mode": "RGB"
  },
  "warnings": ["Image was downscaled for faster processing"]
}
```

### GET /thresholds
View blur classification thresholds.

## Blur Classification

- **Sharp**: Laplacian variance ≥ 500 (clear, focused)
- **Slightly Blurred**: 200-499 (minor blur)
- **Moderately Blurred**: 50-199 (noticeable blur)  
- **Heavily Blurred**: < 50 (severely blurred)

## Performance Optimizations

1. **Automatic Downscaling**: Images > 1MB are resized
2. **Grayscale Conversion**: Reduces processing overhead
3. **Efficient Memory Usage**: Streaming downloads and processing
4. **Async Operations**: Non-blocking I/O for downloads

## Example Usage

```python
import requests

response = requests.post("http://localhost:8000/detect-blur", json={
    "image_url": "https://example.com/test-image.jpg",
    "max_resolution": 1024
})

result = response.json()
print(f"Blur Level: {result['blur_level']}")
print(f"Processing Time: {result['processing_time_ms']}ms")
```

## Error Handling

The API returns detailed error information:
- Download failures (network, HTTP errors)
- Invalid image formats
- Processing errors
- Timeout issues

All errors are captured in the `errors` field of the response.
