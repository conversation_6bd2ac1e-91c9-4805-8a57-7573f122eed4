import http from 'k6/http';
import { check, sleep } from 'k6';
import { Trend, Rate, Counter, Gauge } from 'k6/metrics';

// Performance monitoring metrics
const systemMetrics = {
  cpuUsage: new Gauge('system_cpu_usage'),
  memoryUsage: new Gauge('system_memory_usage'),
  diskUsage: new Gauge('system_disk_usage'),
  networkLatency: new Trend('network_latency'),
  connectionCount: new Gauge('active_connections'),
};

const apiMetrics = {
  blurApi: {
    availability: new Rate('blur_api_availability'),
    responseTime: new Trend('blur_api_response_time'),
    throughput: new Rate('blur_api_throughput'),
    errorRate: new Rate('blur_api_error_rate'),
  },
  inspectorApi: {
    availability: new Rate('inspector_api_availability'),
    responseTime: new Trend('inspector_api_response_time'),
    throughput: new Rate('inspector_api_throughput'),
    errorRate: new Rate('inspector_api_error_rate'),
  }
};

// Configuration
const BLUR_API_URL = 'http://localhost:8000';
const INSPECTOR_API_URL = 'http://localhost:8080';
const MONITORING_INTERVAL = 5; // seconds

export let options = {
  stages: [
    { duration: '24h', target: 1 }, // Long-running monitoring
  ],
  thresholds: {
    'blur_api_availability': ['rate>0.99'], // 99% uptime
    'inspector_api_availability': ['rate>0.99'], // 99% uptime
    'blur_api_response_time': ['p(95)<3000'], // 95% under 3s
    'inspector_api_response_time': ['p(95)<10000'], // 95% under 10s
  },
};

export default function () {
  // Monitor both APIs
  monitorBlurAPI();
  sleep(1);
  monitorInspectorAPI();
  sleep(1);
  
  // Collect system metrics (if available)
  collectSystemMetrics();
  
  // Wait for next monitoring cycle
  sleep(MONITORING_INTERVAL - 2);
}

function monitorBlurAPI() {
  const startTime = Date.now();
  
  // Health check
  const healthResponse = http.get(`${BLUR_API_URL}/health`, {
    timeout: '10s',
  });
  
  const isHealthy = check(healthResponse, {
    'Blur API health check': (r) => r.status === 200,
  });
  
  apiMetrics.blurApi.availability.add(isHealthy);
  apiMetrics.blurApi.responseTime.add(healthResponse.timings.duration);
  
  if (!isHealthy) {
    apiMetrics.blurApi.errorRate.add(1);
    console.error(`Blur API health check failed: ${healthResponse.status}`);
    return;
  }
  
  // Test main endpoint with lightweight request
  const testPayload = {
    image_url: "https://picsum.photos/400/300",
    max_resolution: 512
  };
  
  const testResponse = http.post(`${BLUR_API_URL}/detect-blur`, JSON.stringify(testPayload), {
    headers: { 'Content-Type': 'application/json' },
    timeout: '15s',
  });
  
  const isWorking = check(testResponse, {
    'Blur API functionality': (r) => r.status === 200,
  });
  
  apiMetrics.blurApi.throughput.add(1);
  apiMetrics.blurApi.errorRate.add(!isWorking);
  
  if (isWorking) {
    try {
      const result = JSON.parse(testResponse.body);
      console.log(`Blur API Monitor - Processing: ${result.processing_time_ms}ms, Level: ${result.blur_level}`);
    } catch (e) {
      console.error('Failed to parse blur API response:', e);
    }
  }
}

function monitorInspectorAPI() {
  const startTime = Date.now();
  
  // Health check
  const healthResponse = http.get(`${INSPECTOR_API_URL}/health`, {
    timeout: '10s',
  });
  
  const isHealthy = check(healthResponse, {
    'Inspector API health check': (r) => r.status === 200,
  });
  
  apiMetrics.inspectorApi.availability.add(isHealthy);
  apiMetrics.inspectorApi.responseTime.add(healthResponse.timings.duration);
  
  if (!isHealthy) {
    apiMetrics.inspectorApi.errorRate.add(1);
    console.error(`Inspector API health check failed: ${healthResponse.status}`);
    return;
  }
  
  // Test main endpoint with lightweight request
  const testPayload = {
    url: "https://picsum.photos/400/300",
    is_ocr: false
  };
  
  const testResponse = http.post(`${INSPECTOR_API_URL}/analyze`, JSON.stringify(testPayload), {
    headers: { 'Content-Type': 'application/json' },
    timeout: '20s',
  });
  
  const isWorking = check(testResponse, {
    'Inspector API functionality': (r) => r.status === 200,
  });
  
  apiMetrics.inspectorApi.throughput.add(1);
  apiMetrics.inspectorApi.errorRate.add(!isWorking);
  
  if (isWorking) {
    try {
      const result = JSON.parse(testResponse.body);
      console.log(`Inspector API Monitor - Processing: ${result.processing_time_sec}s, Quality: ${JSON.stringify(result.quality)}`);
    } catch (e) {
      console.error('Failed to parse inspector API response:', e);
    }
  }
}

function collectSystemMetrics() {
  // Note: These would typically require system monitoring tools
  // For demonstration, we'll simulate some metrics
  
  // Simulate CPU usage (would normally come from system monitoring)
  const simulatedCpuUsage = Math.random() * 100;
  systemMetrics.cpuUsage.add(simulatedCpuUsage);
  
  // Simulate memory usage
  const simulatedMemoryUsage = Math.random() * 100;
  systemMetrics.memoryUsage.add(simulatedMemoryUsage);
  
  // Network latency test
  const latencyStart = Date.now();
  const pingResponse = http.get('http://httpbin.org/delay/0', { timeout: '5s' });
  const latency = Date.now() - latencyStart;
  systemMetrics.networkLatency.add(latency);
  
  console.log(`System Metrics - CPU: ${simulatedCpuUsage.toFixed(1)}%, Memory: ${simulatedMemoryUsage.toFixed(1)}%, Latency: ${latency}ms`);
}

export function handleSummary(data) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  
  return {
    [`../results/monitoring-${timestamp}.json`]: JSON.stringify(data, null, 2),
    [`../results/monitoring-${timestamp}.html`]: generateMonitoringReport(data),
    [`../results/monitoring-${timestamp}.csv`]: generateCSVReport(data),
    stdout: generateTextSummary(data),
  };
}

function generateMonitoringReport(data) {
  const metrics = data.metrics;
  const timestamp = new Date().toISOString();
  
  return `
<!DOCTYPE html>
<html>
<head>
    <title>API Performance Monitoring Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .header { text-align: center; color: #333; border-bottom: 2px solid #17a2b8; padding-bottom: 20px; }
        .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8; }
        .metric-title { font-weight: bold; color: #495057; margin-bottom: 10px; }
        .metric-value { font-size: 1.2em; color: #17a2b8; }
        .status-good { border-left-color: #28a745; }
        .status-warning { border-left-color: #ffc107; }
        .status-critical { border-left-color: #dc3545; }
        .api-section { margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #dee2e6; padding: 12px; text-align: left; }
        th { background-color: #e9ecef; font-weight: bold; }
        .chart-placeholder { height: 200px; background: #e9ecef; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 API Performance Monitoring Dashboard</h1>
            <p>Real-time monitoring report | Generated: ${timestamp}</p>
        </div>
        
        <div class="api-section">
            <h2>🔍 Blur Detection API (Port 8000)</h2>
            <div class="dashboard">
                <div class="metric-card ${getStatusClass(metrics.blur_api_availability?.values?.rate || 0, 0.99)}">
                    <div class="metric-title">Availability</div>
                    <div class="metric-value">${((metrics.blur_api_availability?.values?.rate || 0) * 100).toFixed(2)}%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Avg Response Time</div>
                    <div class="metric-value">${(metrics.blur_api_response_time?.values?.avg || 0).toFixed(2)}ms</div>
                </div>
                <div class="metric-card ${getStatusClass(1 - (metrics.blur_api_error_rate?.values?.rate || 0), 0.95)}">
                    <div class="metric-title">Success Rate</div>
                    <div class="metric-value">${((1 - (metrics.blur_api_error_rate?.values?.rate || 0)) * 100).toFixed(2)}%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Throughput</div>
                    <div class="metric-value">${(metrics.blur_api_throughput?.values?.count || 0)} req/min</div>
                </div>
            </div>
        </div>
        
        <div class="api-section">
            <h2>🔍 Image Inspector API (Port 8080)</h2>
            <div class="dashboard">
                <div class="metric-card ${getStatusClass(metrics.inspector_api_availability?.values?.rate || 0, 0.99)}">
                    <div class="metric-title">Availability</div>
                    <div class="metric-value">${((metrics.inspector_api_availability?.values?.rate || 0) * 100).toFixed(2)}%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Avg Response Time</div>
                    <div class="metric-value">${(metrics.inspector_api_response_time?.values?.avg || 0).toFixed(2)}ms</div>
                </div>
                <div class="metric-card ${getStatusClass(1 - (metrics.inspector_api_error_rate?.values?.rate || 0), 0.95)}">
                    <div class="metric-title">Success Rate</div>
                    <div class="metric-value">${((1 - (metrics.inspector_api_error_rate?.values?.rate || 0)) * 100).toFixed(2)}%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Throughput</div>
                    <div class="metric-value">${(metrics.inspector_api_throughput?.values?.count || 0)} req/min</div>
                </div>
            </div>
        </div>
        
        <div class="api-section">
            <h2>🖥️ System Metrics</h2>
            <div class="dashboard">
                <div class="metric-card">
                    <div class="metric-title">CPU Usage</div>
                    <div class="metric-value">${(metrics.system_cpu_usage?.values?.value || 0).toFixed(1)}%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Memory Usage</div>
                    <div class="metric-value">${(metrics.system_memory_usage?.values?.value || 0).toFixed(1)}%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Network Latency</div>
                    <div class="metric-value">${(metrics.network_latency?.values?.avg || 0).toFixed(2)}ms</div>
                </div>
            </div>
        </div>
        
        <h2>📈 Detailed Metrics</h2>
        <table>
            <tr><th>Metric</th><th>Current</th><th>Average</th><th>Min</th><th>Max</th><th>95th %ile</th></tr>
            <tr>
                <td>Blur API Response Time</td>
                <td>${(metrics.blur_api_response_time?.values?.value || 0).toFixed(2)}ms</td>
                <td>${(metrics.blur_api_response_time?.values?.avg || 0).toFixed(2)}ms</td>
                <td>${(metrics.blur_api_response_time?.values?.min || 0).toFixed(2)}ms</td>
                <td>${(metrics.blur_api_response_time?.values?.max || 0).toFixed(2)}ms</td>
                <td>${(metrics.blur_api_response_time?.values?.['p(95)'] || 0).toFixed(2)}ms</td>
            </tr>
            <tr>
                <td>Inspector API Response Time</td>
                <td>${(metrics.inspector_api_response_time?.values?.value || 0).toFixed(2)}ms</td>
                <td>${(metrics.inspector_api_response_time?.values?.avg || 0).toFixed(2)}ms</td>
                <td>${(metrics.inspector_api_response_time?.values?.min || 0).toFixed(2)}ms</td>
                <td>${(metrics.inspector_api_response_time?.values?.max || 0).toFixed(2)}ms</td>
                <td>${(metrics.inspector_api_response_time?.values?.['p(95)'] || 0).toFixed(2)}ms</td>
            </tr>
        </table>
    </div>
    
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(() => location.reload(), 30000);
    </script>
</body>
</html>`;
}

function getStatusClass(value, threshold) {
  if (value >= threshold) return 'status-good';
  if (value >= threshold * 0.9) return 'status-warning';
  return 'status-critical';
}

function generateCSVReport(data) {
  const metrics = data.metrics;
  const timestamp = new Date().toISOString();
  
  let csv = 'Timestamp,Metric,Value,Unit\n';
  csv += `${timestamp},blur_api_availability,${(metrics.blur_api_availability?.values?.rate || 0) * 100},percent\n`;
  csv += `${timestamp},blur_api_response_time,${metrics.blur_api_response_time?.values?.avg || 0},ms\n`;
  csv += `${timestamp},blur_api_error_rate,${(metrics.blur_api_error_rate?.values?.rate || 0) * 100},percent\n`;
  csv += `${timestamp},inspector_api_availability,${(metrics.inspector_api_availability?.values?.rate || 0) * 100},percent\n`;
  csv += `${timestamp},inspector_api_response_time,${metrics.inspector_api_response_time?.values?.avg || 0},ms\n`;
  csv += `${timestamp},inspector_api_error_rate,${(metrics.inspector_api_error_rate?.values?.rate || 0) * 100},percent\n`;
  csv += `${timestamp},system_cpu_usage,${metrics.system_cpu_usage?.values?.value || 0},percent\n`;
  csv += `${timestamp},system_memory_usage,${metrics.system_memory_usage?.values?.value || 0},percent\n`;
  csv += `${timestamp},network_latency,${metrics.network_latency?.values?.avg || 0},ms\n`;
  
  return csv;
}

function generateTextSummary(data) {
  const metrics = data.metrics;
  return `
=== API PERFORMANCE MONITORING SUMMARY ===
Timestamp: ${new Date().toISOString()}

Blur Detection API:
- Availability: ${((metrics.blur_api_availability?.values?.rate || 0) * 100).toFixed(2)}%
- Avg Response Time: ${(metrics.blur_api_response_time?.values?.avg || 0).toFixed(2)}ms
- Error Rate: ${((metrics.blur_api_error_rate?.values?.rate || 0) * 100).toFixed(2)}%

Image Inspector API:
- Availability: ${((metrics.inspector_api_availability?.values?.rate || 0) * 100).toFixed(2)}%
- Avg Response Time: ${(metrics.inspector_api_response_time?.values?.avg || 0).toFixed(2)}ms
- Error Rate: ${((metrics.inspector_api_error_rate?.values?.rate || 0) * 100).toFixed(2)}%

System Metrics:
- CPU Usage: ${(metrics.system_cpu_usage?.values?.value || 0).toFixed(1)}%
- Memory Usage: ${(metrics.system_memory_usage?.values?.value || 0).toFixed(1)}%
- Network Latency: ${(metrics.network_latency?.values?.avg || 0).toFixed(2)}ms
`;
}
