# Blur Detection API Stress Tests

This directory contains comprehensive stress tests for the FastAPI Blur Detection service running on port 8000.

## Test Scripts

### 1. `load-test.js` - Main Load Testing Script
Comprehensive load testing with multiple scenarios and realistic user behavior simulation.

**Features:**
- Multiple test scenarios (health checks, basic blur detection, varied image sizes, batch processing)
- Error handling validation
- Custom metrics tracking
- Realistic user think time simulation
- Retry logic for transient failures

**Usage:**
```bash
# Run with default moderate load scenario
k6 run load-test.js

# Run with specific scenario
k6 run -e SCENARIO=light_load load-test.js
k6 run -e SCENARIO=heavy_load load-test.js
k6 run -e SCENARIO=spike_test load-test.js
```

### 2. `spike-test.js` - Spike Testing
Tests sudden traffic increases to validate system resilience.

### 3. `soak-test.js` - Endurance Testing  
Long-running tests to identify memory leaks and performance degradation.

### 4. `volume-test.js` - High Volume Testing
Tests with large payloads and high-resolution images.

## Available Scenarios

- **light_load**: 10 users, 2 minutes
- **moderate_load**: 50 users, 5 minutes  
- **heavy_load**: 100 users, 10 minutes
- **spike_test**: Sudden spike to 200 users
- **soak_test**: 25 users for 30 minutes
- **stress_test**: Progressive load increase

## Test Endpoints Covered

1. **GET /** - Root endpoint with API information
2. **GET /health** - Health check endpoint
3. **GET /thresholds** - Blur classification thresholds
4. **POST /detect-blur** - Main blur detection endpoint

## Payload Variations

The tests use various payload configurations:
- **Basic**: Standard resolution (1024px max)
- **High Resolution**: 2048px max resolution
- **Low Resolution**: 512px max resolution
- **Different image sizes**: Small, medium, large images
- **Error cases**: Invalid URLs, malformed JSON

## Metrics Tracked

### Standard HTTP Metrics
- Response time (avg, min, max, percentiles)
- Request rate (requests per second)
- Error rate (failed requests percentage)
- Data transfer rates

### Custom Blur Detection Metrics
- `blur_detection_duration`: Time for blur detection requests
- `blur_detection_errors`: Error rate for blur detection
- `blur_detection_requests`: Count of blur detection requests
- `image_processing_time`: Server-side processing time

## Performance Thresholds

- **Response Time**: 95% of requests under 3 seconds
- **Error Rate**: Less than 5% failures
- **Processing Time**: 90% of image processing under 2 seconds
- **Availability**: Health checks should always succeed

## Running Tests

### Prerequisites
1. Ensure Blur Detection API is running on port 8000
2. Install k6: `choco install k6` (Windows) or download from k6.io

### Basic Test Execution
```bash
# Navigate to blur detection test directory
cd stress-tests/scripts/blur-detection

# Run baseline performance test first
k6 run ../utils/baseline.js

# Run load test with moderate load
k6 run load-test.js

# Run with custom scenario
k6 run -e SCENARIO=heavy_load load-test.js
```

### Advanced Options
```bash
# Run with custom VU count and duration
k6 run --vus 75 --duration 8m load-test.js

# Run with specific stages
k6 run --stage 1m:10,3m:50,1m:0 load-test.js

# Output results to specific file
k6 run --out json=results.json load-test.js
```

## Interpreting Results

### Success Criteria
- ✅ Error rate < 5%
- ✅ 95th percentile response time < 3000ms
- ✅ All health checks pass
- ✅ No memory leaks in soak tests

### Warning Signs
- ⚠️ Error rate 5-10%
- ⚠️ Response time degradation over time
- ⚠️ High variance in processing times
- ⚠️ Timeouts on high-resolution images

### Critical Issues
- ❌ Error rate > 10%
- ❌ Response times > 5 seconds
- ❌ Service unavailability
- ❌ Memory leaks or crashes

## Troubleshooting

### Common Issues
1. **Connection refused**: Ensure API is running on port 8000
2. **High error rates**: Check API logs for specific errors
3. **Slow responses**: Monitor CPU/memory usage on API server
4. **Timeouts**: Increase timeout values for high-resolution tests

### Debug Mode
```bash
# Run with verbose logging
k6 run --verbose load-test.js

# Run single iteration for debugging
k6 run --iterations 1 load-test.js
```

## Report Generation

Tests automatically generate:
- **JSON reports**: Detailed metrics in `../../results/`
- **HTML reports**: Visual dashboards with charts
- **Console output**: Real-time progress and summary

Example report files:
- `blur-detection-moderate-load-2024-01-15T10-30-00.json`
- `blur-detection-moderate-load-2024-01-15T10-30-00.html`
