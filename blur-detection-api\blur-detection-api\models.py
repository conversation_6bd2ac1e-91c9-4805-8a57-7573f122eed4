from pydantic import BaseModel, HttpUrl
from typing import Optional, Dict, Any
from enum import Enum

class BlurLevel(str, Enum):
    SHARP = "sharp"
    SLIGHTLY_BLURRED = "slightly_blurred"
    MODERATELY_BLURRED = "moderately_blurred"
    HEAVILY_BLURRED = "heavily_blurred"

class ImageRequest(BaseModel):
    image_url: HttpUrl
    max_resolution: Optional[int] = 1024  # Max width/height for downscaling

class BlurDetectionResponse(BaseModel):
    laplacian_variance: float
    blur_level: BlurLevel
    is_blurred: bool
    confidence_score: float
    processing_time_ms: float
    image_info: Dict[str, Any]
    overexposed: bool
    oversaturated: bool
    incorrect_wb: bool
    errors: Optional[list] = None
    warnings: Optional[list] = None

class ImageInfo(BaseModel):
    original_size_bytes: int
    original_dimensions: tuple
    processed_dimensions: tuple
    was_downscaled: bool
    format: str
    mode: str
