import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { 
  getRandomPayload, 
  validateResponse, 
  simulateUserThinkTime,
  logPerformanceMetrics 
} from '../utils/common.js';

// Custom metrics for spike testing
const spikeErrors = new Rate('spike_errors');
const spikeDuration = new Trend('spike_duration');
const spikeRequests = new Counter('spike_requests');
const recoveryTime = new Trend('recovery_time');

// Configuration
const BASE_URL = 'http://localhost:8000';

// Spike test configuration - sudden load increase
export let options = {
  stages: [
    { duration: '2m', target: 10 },   // Normal load
    { duration: '30s', target: 200 }, // Sudden spike
    { duration: '3m', target: 200 },  // Maintain spike
    { duration: '30s', target: 10 },  // Quick recovery
    { duration: '2m', target: 10 },   // Recovery period
  ],
  thresholds: {
    'spike_errors': ['rate<0.1'], // Allow higher error rate during spike
    'spike_duration': ['p(95)<10000'], // 95% under 10 seconds during spike
    'http_req_duration': ['p(99)<15000'], // 99% under 15 seconds
    'recovery_time': ['p(90)<3000'], // Recovery should be fast
  },
};

let spikeStartTime = null;
let spikeEndTime = null;
let isInSpike = false;

export default function () {
  // Detect if we're in the spike phase based on current VU count
  const currentVUs = __ENV.K6_VUS || 1;
  const wasInSpike = isInSpike;
  isInSpike = currentVUs > 50;
  
  // Mark spike start/end times
  if (!wasInSpike && isInSpike && !spikeStartTime) {
    spikeStartTime = Date.now();
    console.log('🚀 SPIKE PHASE STARTED');
  } else if (wasInSpike && !isInSpike && !spikeEndTime) {
    spikeEndTime = Date.now();
    console.log('📉 SPIKE PHASE ENDED');
  }
  
  // Run different test patterns based on phase
  if (isInSpike) {
    runSpikePhaseTest();
  } else {
    runNormalPhaseTest();
  }
  
  // Shorter think time during spike to increase pressure
  const thinkTime = isInSpike ? [0.5, 1.5] : [1, 3];
  simulateUserThinkTime(thinkTime[0], thinkTime[1]);
}

function runSpikePhaseTest() {
  // During spike, focus on core functionality with minimal delays
  const testScenarios = [
    testBlurDetectionFast,
    testHealthCheck,
    testBatchRequests
  ];
  
  const scenario = testScenarios[Math.floor(Math.random() * testScenarios.length)];
  scenario();
}

function runNormalPhaseTest() {
  // During normal phase, run comprehensive tests
  const testScenarios = [
    testBlurDetectionBasic,
    testHealthCheck,
    testThresholds,
    testErrorHandling
  ];
  
  const scenario = testScenarios[Math.floor(Math.random() * testScenarios.length)];
  scenario();
}

function testBlurDetectionFast() {
  const payload = getRandomPayload('blur_detection', 'basic');
  const startTime = Date.now();
  
  const response = http.post(`${BASE_URL}/detect-blur`, JSON.stringify(payload), {
    headers: { 'Content-Type': 'application/json' },
    timeout: '20s', // Shorter timeout during spike
  });
  
  const duration = Date.now() - startTime;
  const isSuccess = check(response, {
    'Spike blur detection status': (r) => r.status === 200,
    'Spike response time reasonable': (r) => r.timings.duration < 15000,
  });
  
  // Record spike-specific metrics
  spikeErrors.add(!isSuccess);
  spikeDuration.add(response.timings.duration);
  spikeRequests.add(1);
  
  // If we're recovering from spike, measure recovery time
  if (!isInSpike && spikeEndTime && (Date.now() - spikeEndTime) < 120000) {
    recoveryTime.add(response.timings.duration);
  }
  
  if (isSuccess && response.body) {
    try {
      const result = JSON.parse(response.body);
      logPerformanceMetrics(response, `spike_blur_detection_${result.blur_level}`);
    } catch (e) {
      console.error('Failed to parse spike response:', e);
    }
  }
}

function testBlurDetectionBasic() {
  const payload = getRandomPayload('blur_detection', 'basic');
  
  const response = http.post(`${BASE_URL}/detect-blur`, JSON.stringify(payload), {
    headers: { 'Content-Type': 'application/json' },
    timeout: '30s',
  });
  
  const isSuccess = validateResponse(response, 200, 'Normal Blur Detection');
  
  spikeErrors.add(!isSuccess);
  spikeRequests.add(1);
  
  if (isSuccess && response.body) {
    try {
      const result = JSON.parse(response.body);
      console.log(`Normal Phase - Variance: ${result.laplacian_variance}, Level: ${result.blur_level}`);
    } catch (e) {
      console.error('Failed to parse normal response:', e);
    }
  }
}

function testHealthCheck() {
  const response = http.get(`${BASE_URL}/health`, { timeout: '10s' });
  
  const isSuccess = check(response, {
    'Health check during spike': (r) => r.status === 200,
    'Health check fast response': (r) => r.timings.duration < 5000,
  });
  
  spikeErrors.add(!isSuccess);
  spikeRequests.add(1);
  
  if (!isSuccess) {
    console.error(`Health check failed during ${isInSpike ? 'spike' : 'normal'} phase: ${response.status}`);
  }
}

function testThresholds() {
  const response = http.get(`${BASE_URL}/thresholds`, { timeout: '10s' });
  
  const isSuccess = validateResponse(response, 200, 'Thresholds');
  spikeErrors.add(!isSuccess);
  spikeRequests.add(1);
}

function testBatchRequests() {
  // Simulate multiple concurrent requests during spike
  const batchSize = isInSpike ? 2 : 3; // Smaller batches during spike
  const requests = [];
  
  for (let i = 0; i < batchSize; i++) {
    const payload = getRandomPayload('blur_detection', 'basic');
    requests.push([
      'POST',
      `${BASE_URL}/detect-blur`,
      JSON.stringify(payload),
      { headers: { 'Content-Type': 'application/json' } }
    ]);
  }
  
  const responses = http.batch(requests);
  
  responses.forEach((response, index) => {
    const isSuccess = check(response, {
      [`Batch request ${index + 1} status`]: (r) => r.status === 200,
      [`Batch request ${index + 1} response time`]: (r) => r.timings.duration < (isInSpike ? 20000 : 10000),
    });
    
    spikeErrors.add(!isSuccess);
    spikeRequests.add(1);
  });
}

function testErrorHandling() {
  // Test with invalid payload to see how system handles errors under load
  const invalidPayload = {
    image_url: "https://invalid-url-for-spike-test.com/image.jpg",
    max_resolution: 1024
  };
  
  const response = http.post(`${BASE_URL}/detect-blur`, JSON.stringify(invalidPayload), {
    headers: { 'Content-Type': 'application/json' },
    timeout: '15s',
  });
  
  const isHandledGracefully = check(response, {
    'Error handled during spike': (r) => r.status >= 400 && r.status < 600,
    'Error response time reasonable': (r) => r.timings.duration < 10000,
  });
  
  // Don't count expected errors as failures
  spikeRequests.add(1);
}

export function handleSummary(data) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  
  return {
    [`../../results/blur-detection-spike-test-${timestamp}.json`]: JSON.stringify(data, null, 2),
    [`../../results/blur-detection-spike-test-${timestamp}.html`]: htmlReport(data),
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
  };
}

function htmlReport(data) {
  const metrics = data.metrics;
  const timestamp = new Date().toISOString();
  
  return `
<!DOCTYPE html>
<html>
<head>
    <title>Blur Detection API - Spike Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .header { text-align: center; color: #333; border-bottom: 2px solid #e74c3c; padding-bottom: 20px; }
        .spike-phase { background: #ffebee; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #e74c3c; }
        .normal-phase { background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4caf50; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #e74c3c; }
        .metric-title { font-weight: bold; color: #495057; margin-bottom: 10px; }
        .metric-value { font-size: 1.2em; color: #e74c3c; }
        .success { border-left-color: #28a745; }
        .warning { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #dee2e6; padding: 12px; text-align: left; }
        th { background-color: #e9ecef; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ Blur Detection API Spike Test Report</h1>
            <p>Sudden load increase testing | Generated: ${timestamp}</p>
        </div>
        
        <div class="spike-phase">
            <h2>🚀 Spike Test Overview</h2>
            <p>This test simulates a sudden increase in traffic from 10 to 200 concurrent users to evaluate system resilience and recovery capabilities.</p>
            <p><strong>Test Pattern:</strong> 2m normal → 30s spike ramp → 3m spike maintain → 30s recovery → 2m normal</p>
        </div>
        
        <div class="metric-grid">
            <div class="metric-card ${(metrics.http_req_failed?.values?.rate || 0) < 0.1 ? 'success' : 'error'}">
                <div class="metric-title">Overall Success Rate</div>
                <div class="metric-value">${((1 - (metrics.http_req_failed?.values?.rate || 0)) * 100).toFixed(2)}%</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">Total Requests</div>
                <div class="metric-value">${metrics.http_reqs?.values?.count || 0}</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">Spike Requests</div>
                <div class="metric-value">${metrics.spike_requests?.values?.count || 0}</div>
            </div>
            <div class="metric-card ${(metrics.spike_errors?.values?.rate || 0) < 0.1 ? 'success' : 'warning'}">
                <div class="metric-title">Spike Error Rate</div>
                <div class="metric-value">${((metrics.spike_errors?.values?.rate || 0) * 100).toFixed(2)}%</div>
            </div>
        </div>
        
        <h2>📊 Performance During Spike</h2>
        <table>
            <tr><th>Metric</th><th>Average</th><th>Min</th><th>Max</th><th>95th %ile</th><th>99th %ile</th></tr>
            <tr>
                <td>HTTP Request Duration</td>
                <td>${(metrics.http_req_duration?.values?.avg || 0).toFixed(2)}ms</td>
                <td>${(metrics.http_req_duration?.values?.min || 0).toFixed(2)}ms</td>
                <td>${(metrics.http_req_duration?.values?.max || 0).toFixed(2)}ms</td>
                <td>${(metrics.http_req_duration?.values?.['p(95)'] || 0).toFixed(2)}ms</td>
                <td>${(metrics.http_req_duration?.values?.['p(99)'] || 0).toFixed(2)}ms</td>
            </tr>
            <tr>
                <td>Spike Duration</td>
                <td>${(metrics.spike_duration?.values?.avg || 0).toFixed(2)}ms</td>
                <td>${(metrics.spike_duration?.values?.min || 0).toFixed(2)}ms</td>
                <td>${(metrics.spike_duration?.values?.max || 0).toFixed(2)}ms</td>
                <td>${(metrics.spike_duration?.values?.['p(95)'] || 0).toFixed(2)}ms</td>
                <td>${(metrics.spike_duration?.values?.['p(99)'] || 0).toFixed(2)}ms</td>
            </tr>
            <tr>
                <td>Recovery Time</td>
                <td>${(metrics.recovery_time?.values?.avg || 0).toFixed(2)}ms</td>
                <td>${(metrics.recovery_time?.values?.min || 0).toFixed(2)}ms</td>
                <td>${(metrics.recovery_time?.values?.max || 0).toFixed(2)}ms</td>
                <td>${(metrics.recovery_time?.values?.['p(90)'] || 0).toFixed(2)}ms</td>
                <td>${(metrics.recovery_time?.values?.['p(95)'] || 0).toFixed(2)}ms</td>
            </tr>
        </table>
        
        <div class="normal-phase">
            <h2>✅ Spike Test Results</h2>
            <ul>
                <li><strong>System Resilience:</strong> ${(metrics.spike_errors?.values?.rate || 0) < 0.1 ? 'GOOD' : 'NEEDS IMPROVEMENT'} - Error rate during spike was ${((metrics.spike_errors?.values?.rate || 0) * 100).toFixed(2)}%</li>
                <li><strong>Response Time:</strong> ${(metrics.spike_duration?.values?.['p(95)'] || 0) < 10000 ? 'ACCEPTABLE' : 'SLOW'} - 95th percentile was ${(metrics.spike_duration?.values?.['p(95)'] || 0).toFixed(0)}ms</li>
                <li><strong>Recovery:</strong> ${(metrics.recovery_time?.values?.['p(90)'] || 0) < 3000 ? 'FAST' : 'SLOW'} - 90th percentile recovery time was ${(metrics.recovery_time?.values?.['p(90)'] || 0).toFixed(0)}ms</li>
                <li><strong>Availability:</strong> System remained responsive throughout the spike test</li>
            </ul>
        </div>
    </div>
</body>
</html>`;
}

function textSummary(data, options) {
  const metrics = data.metrics;
  return `
=== BLUR DETECTION API SPIKE TEST SUMMARY ===
Total Requests: ${metrics.http_reqs?.values?.count || 0}
Spike Requests: ${metrics.spike_requests?.values?.count || 0}
Overall Success Rate: ${((1 - (metrics.http_req_failed?.values?.rate || 0)) * 100).toFixed(2)}%
Spike Error Rate: ${((metrics.spike_errors?.values?.rate || 0) * 100).toFixed(2)}%

Performance During Spike:
- Average Response Time: ${(metrics.spike_duration?.values?.avg || 0).toFixed(2)}ms
- 95th Percentile: ${(metrics.spike_duration?.values?.['p(95)'] || 0).toFixed(2)}ms
- Max Response Time: ${(metrics.spike_duration?.values?.max || 0).toFixed(2)}ms

Recovery Performance:
- Average Recovery Time: ${(metrics.recovery_time?.values?.avg || 0).toFixed(2)}ms
- 90th Percentile Recovery: ${(metrics.recovery_time?.values?.['p(90)'] || 0).toFixed(2)}ms

System handled the spike ${(metrics.spike_errors?.values?.rate || 0) < 0.1 ? 'WELL' : 'POORLY'} with ${((metrics.spike_errors?.values?.rate || 0) * 100).toFixed(2)}% error rate.
`;
}
