# API Stress Testing - Complete Usage Guide

This comprehensive guide covers everything you need to know about running stress tests for your APIs.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Installation](#installation)
3. [Test Execution](#test-execution)
4. [Understanding Results](#understanding-results)
5. [Troubleshooting](#troubleshooting)
6. [Advanced Usage](#advanced-usage)
7. [Best Practices](#best-practices)

## Quick Start

### 1. Prerequisites Check
```bash
# Verify k6 is installed
k6 version

# Check if APIs are running
curl http://localhost:8000/health  # Blur Detection API
curl http://localhost:8080/health  # Image Inspector API
```

### 2. Run Your First Test
```bash
cd stress-tests

# Run baseline performance test
k6 run scripts/utils/baseline.js

# Run a simple load test
k6 run scripts/blur-detection/load-test.js
```

### 3. View Results
- Check console output for immediate results
- Open generated HTML reports in `results/` directory
- Use `results/dashboard.html` for comprehensive overview

## Installation

### Install k6

**Windows:**
```bash
# Using Chocolatey
choco install k6

# Using Scoop
scoop install k6

# Manual download from https://github.com/grafana/k6/releases
```

**macOS:**
```bash
# Using Homebrew
brew install k6
```

**Linux:**
```bash
# Ubuntu/Debian
sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
sudo apt-get update
sudo apt-get install k6
```

### Verify Installation
```bash
k6 version
# Should output: k6 v0.x.x
```

## Test Execution

### Automated Test Suite
Run all tests with comprehensive reporting:
```bash
./run-all-tests.bat
```

This will:
- Check API availability
- Run baseline tests
- Execute load tests for both APIs
- Run spike tests
- Generate comprehensive reports

### Interactive Test Selection
Choose specific tests interactively:
```bash
./run-specific-test.bat
```

Follow the menu to select:
- Which API to test
- What scenario to run
- View real-time results

### Manual Test Execution

**Basic Load Tests:**
```bash
# Blur Detection API
k6 run scripts/blur-detection/load-test.js

# Image Inspector API
k6 run scripts/image-inspector/load-test.js
```

**Specific Scenarios:**
```bash
# Light load (10 users, 2 minutes)
k6 run -e SCENARIO=light_load scripts/blur-detection/load-test.js

# Heavy load (100 users, 10 minutes)
k6 run -e SCENARIO=heavy_load scripts/image-inspector/load-test.js

# Spike test (sudden load increase)
k6 run scripts/blur-detection/spike-test.js
```

**Custom Parameters:**
```bash
# Custom virtual users and duration
k6 run --vus 75 --duration 8m scripts/blur-detection/load-test.js

# Custom stages
k6 run --stage 1m:10,5m:50,1m:0 scripts/image-inspector/load-test.js
```

### Continuous Monitoring
```bash
# Long-running performance monitoring
k6 run monitoring/performance-monitor.js
```

## Understanding Results

### Key Metrics

**Response Time Metrics:**
- `avg`: Average response time
- `min`: Fastest response
- `max`: Slowest response
- `p(90)`: 90th percentile (90% of requests faster than this)
- `p(95)`: 95th percentile (95% of requests faster than this)
- `p(99)`: 99th percentile (99% of requests faster than this)

**Throughput Metrics:**
- `http_reqs`: Total number of HTTP requests
- `http_req_rate`: Requests per second
- `data_received`: Total data downloaded
- `data_sent`: Total data uploaded

**Error Metrics:**
- `http_req_failed`: Percentage of failed requests
- `http_req_blocked`: Time spent blocked (DNS lookup, TCP connect, etc.)

### Success Criteria

**Blur Detection API:**
- ✅ Error rate < 5%
- ✅ 95th percentile response time < 3000ms
- ✅ Health checks always succeed
- ✅ Processing time < 2000ms for 90% of requests

**Image Inspector API:**
- ✅ Error rate < 5%
- ✅ 95th percentile response time < 10000ms
- ✅ Health checks always succeed
- ✅ Endpoint-specific thresholds met

### Report Types

**1. Console Output:**
Real-time progress and final summary displayed in terminal.

**2. JSON Reports:**
Raw metrics data saved to `results/` directory:
```json
{
  "metrics": {
    "http_req_duration": {
      "values": {
        "avg": 1234.56,
        "p(95)": 2345.67
      }
    }
  }
}
```

**3. HTML Reports:**
Visual dashboards with charts and graphs saved to `results/` directory.

**4. CSV Reports:**
Time-series data for trending analysis.

## Troubleshooting

### Common Issues

#### 1. "Connection Refused" Errors
**Problem:** Cannot connect to API
```
ERRO[0001] Get "http://localhost:8000/health": dial tcp: connect: connection refused
```

**Solutions:**
```bash
# Check if API is running
netstat -an | findstr :8000
netstat -an | findstr :8080

# Start Blur Detection API
cd blur-detection-api/blur-detection-api
python main.py

# Start Image Inspector API
cd image-inspector-go-main
go run cmd/api/main.go
```

#### 2. High Error Rates
**Problem:** Error rate > 10% during tests

**Possible Causes:**
- API server overloaded
- Invalid test image URLs
- Network connectivity issues
- API configuration problems

**Solutions:**
- Reduce concurrent users: `k6 run --vus 10 script.js`
- Check API server logs
- Verify test image URLs are accessible
- Monitor server resources

#### 3. Slow Response Times
**Problem:** Response times above thresholds

**Solutions:**
- Check server CPU/memory usage
- Optimize API configuration
- Use smaller test images
- Increase timeout values

#### 4. k6 Script Errors
**Problem:** Script execution failures

**Solutions:**
```bash
# Verify k6 installation
k6 version

# Check file permissions
ls -la scripts/

# Validate JSON configuration
node -e "console.log(JSON.parse(require('fs').readFileSync('config/test-scenarios.json')))"

# Run with verbose logging
k6 run --verbose scripts/blur-detection/load-test.js
```

### Debug Mode

**Verbose Logging:**
```bash
k6 run --verbose scripts/blur-detection/load-test.js
```

**Single Iteration:**
```bash
k6 run --iterations 1 scripts/blur-detection/load-test.js
```

**HTTP Debug:**
```bash
k6 run --http-debug scripts/blur-detection/load-test.js
```

## Advanced Usage

### Custom Test Scenarios

**1. Modify Configuration:**
Edit `config/test-scenarios.json`:
```json
{
  "scenarios": {
    "custom_scenario": {
      "name": "Custom Load Test",
      "virtual_users": 25,
      "duration": "3m",
      "ramp_up": "45s",
      "ramp_down": "45s"
    }
  }
}
```

**2. Use Custom Scenario:**
```bash
k6 run -e SCENARIO=custom_scenario scripts/blur-detection/load-test.js
```

### Environment Variables

**Custom API URLs:**
```bash
k6 run -e BLUR_API_URL=http://api.example.com:8000 scripts/blur-detection/load-test.js
```

**Debug Mode:**
```bash
k6 run -e DEBUG=true scripts/blur-detection/load-test.js
```

### Output Formats

**JSON Output:**
```bash
k6 run --out json=results.json scripts/blur-detection/load-test.js
```

**CSV Output:**
```bash
k6 run --out csv=results.csv scripts/blur-detection/load-test.js
```

**Multiple Outputs:**
```bash
k6 run --out json=results.json --out csv=results.csv scripts/blur-detection/load-test.js
```

## Best Practices

### Test Design
1. **Start Small:** Begin with light load tests
2. **Gradual Increase:** Progressively increase load
3. **Realistic Data:** Use representative test images
4. **Think Time:** Include realistic user delays
5. **Error Handling:** Test error scenarios

### Performance Optimization
1. **Connection Pooling:** Configure HTTP client pools
2. **Caching:** Implement response caching
3. **Resource Limits:** Set appropriate limits
4. **Monitoring:** Track system resources
5. **Load Balancing:** Use multiple instances

### Test Execution
1. **Baseline First:** Always run baseline tests
2. **Consistent Environment:** Use same test environment
3. **Multiple Runs:** Run tests multiple times
4. **Documentation:** Document test results
5. **Trend Analysis:** Track performance over time

### Monitoring
1. **Real-time Monitoring:** Use performance monitor
2. **Alerting:** Set up performance alerts
3. **Dashboards:** Use visual dashboards
4. **Metrics Export:** Export to monitoring systems
5. **Regular Testing:** Schedule regular tests

## Getting Help

### Resources
- [k6 Documentation](https://k6.io/docs/)
- [API Documentation](../README.md)
- [Test Scripts](scripts/)
- [Configuration Files](config/)

### Support
- Check existing issues in project repository
- Review API server logs
- Use debug mode for detailed information
- Monitor system resources during tests

### Contributing
- Report issues with detailed information
- Suggest improvements to test scenarios
- Share performance optimization tips
- Contribute new test scripts
