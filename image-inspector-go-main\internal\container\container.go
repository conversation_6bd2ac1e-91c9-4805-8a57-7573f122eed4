package container

import (
	"fmt"
	"net/http"

	"github.com/anime-shed/image-inspector-go/internal/analyzer"
	"github.com/anime-shed/image-inspector-go/internal/config"
	"github.com/anime-shed/image-inspector-go/internal/repository"
	"github.com/anime-shed/image-inspector-go/internal/service"
	"github.com/anime-shed/image-inspector-go/internal/storage"
	"github.com/anime-shed/image-inspector-go/internal/transport"
)

// Container holds all application dependencies using dependency injection
type Container struct {
	config          *config.Config
	imageFetcher    storage.ImageFetcher
	imageAnalyzer   analyzer.ImageAnalyzer
	imageRepository repository.ImageRepository
	analysisService service.ImageAnalysisService
	handler         http.Handler
}

// NewContainer creates and initializes all dependencies using dependency injection
func NewContainer(cfg *config.Config) (*Container, error) {
	// Add nil config guard
	if cfg == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	// Create image fetcher
	imageFetcher := storage.NewHTTPImageFetcher(cfg.ImageFetchTimeout)

	// Create single image analyzer (remove duplication)
	imageAnalyzer, err := analyzer.NewCoreAnalyzer()
	if err != nil {
		return nil, err
	}

	// Create image repository
	imageRepository := repository.NewHTTPImageRepository(imageFetcher, cfg.ImageFetchTimeout)

	// Create analysis service (single service for both endpoints)
	analysisService := service.NewImageAnalysisService(imageRepository, imageAnalyzer)

	// Create HTTP handler with service
	handler := transport.NewHandler(analysisService, cfg)

	return &Container{
		config:          cfg,
		imageFetcher:    imageFetcher,
		imageAnalyzer:   imageAnalyzer,
		imageRepository: imageRepository,
		analysisService: analysisService,
		handler:         handler,
	}, nil
}

// Handler returns the HTTP handler
func (c *Container) Handler() http.Handler {
	return c.handler
}

// Config returns the configuration
func (c *Container) Config() *config.Config {
	return c.config
}

// GetAnalysisService returns the analysis service
func (c *Container) GetAnalysisService() service.ImageAnalysisService {
	return c.analysisService
}

// Close shuts down the container and releases resources
func (c *Container) Close() error {
	// Close the image analyzer if it has a Close method
	if closer, ok := c.imageAnalyzer.(interface{ Close() error }); ok {
		return closer.Close()
	}
	return nil
}
