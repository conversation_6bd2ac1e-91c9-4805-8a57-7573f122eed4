@echo off
echo ========================================
echo    API Stress Testing Suite
echo ========================================
echo.

REM Check if k6 is installed
k6 version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: k6 is not installed or not in PATH
    echo Please install k6 from https://k6.io/docs/get-started/installation/
    echo.
    echo For Windows: choco install k6
    pause
    exit /b 1
)

REM Create results directory if it doesn't exist
if not exist "results" mkdir results

echo Current time: %date% %time%
echo.

REM Check if APIs are running
echo Checking API availability...
echo.

REM Check Blur Detection API (port 8000)
curl -s http://localhost:8000/health >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Blur Detection API not responding on port 8000
    echo Please start the API: cd blur-detection-api/blur-detection-api && python main.py
    echo.
) else (
    echo ✓ Blur Detection API is running on port 8000
)

REM Check Image Inspector API (port 8080)
curl -s http://localhost:8080/health >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Image Inspector API not responding on port 8080
    echo Please start the API: cd image-inspector-go-main && go run cmd/api/main.go
    echo.
) else (
    echo ✓ Image Inspector API is running on port 8080
)

echo.
echo ========================================
echo    Running Baseline Performance Tests
echo ========================================
echo.

REM Run baseline tests first
echo Running baseline performance measurement...
k6 run scripts/utils/baseline.js
if %errorlevel% neq 0 (
    echo ERROR: Baseline test failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Running Load Tests
echo ========================================
echo.

REM Run Blur Detection API tests
echo.
echo --- Blur Detection API Tests ---
echo.

echo Running light load test for Blur Detection API...
k6 run -e SCENARIO=light_load scripts/blur-detection/load-test.js
if %errorlevel% neq 0 echo WARNING: Light load test failed for Blur Detection API

echo.
echo Running moderate load test for Blur Detection API...
k6 run -e SCENARIO=moderate_load scripts/blur-detection/load-test.js
if %errorlevel% neq 0 echo WARNING: Moderate load test failed for Blur Detection API

echo.
echo Running heavy load test for Blur Detection API...
k6 run -e SCENARIO=heavy_load scripts/blur-detection/load-test.js
if %errorlevel% neq 0 echo WARNING: Heavy load test failed for Blur Detection API

REM Run Image Inspector API tests
echo.
echo --- Image Inspector API Tests ---
echo.

echo Running light load test for Image Inspector API...
k6 run -e SCENARIO=light_load scripts/image-inspector/load-test.js
if %errorlevel% neq 0 echo WARNING: Light load test failed for Image Inspector API

echo.
echo Running moderate load test for Image Inspector API...
k6 run -e SCENARIO=moderate_load scripts/image-inspector/load-test.js
if %errorlevel% neq 0 echo WARNING: Moderate load test failed for Image Inspector API

echo.
echo Running heavy load test for Image Inspector API...
k6 run -e SCENARIO=heavy_load scripts/image-inspector/load-test.js
if %errorlevel% neq 0 echo WARNING: Heavy load test failed for Image Inspector API

echo.
echo ========================================
echo    Running Spike Tests
echo ========================================
echo.

echo Running spike test for Blur Detection API...
k6 run -e SCENARIO=spike_test scripts/blur-detection/load-test.js
if %errorlevel% neq 0 echo WARNING: Spike test failed for Blur Detection API

echo.
echo Running spike test for Image Inspector API...
k6 run -e SCENARIO=spike_test scripts/image-inspector/load-test.js
if %errorlevel% neq 0 echo WARNING: Spike test failed for Image Inspector API

echo.
echo ========================================
echo    Generating Reports
echo ========================================
echo.

REM Generate comprehensive dashboard
echo Generating comprehensive dashboard...
node monitoring/dashboard-generator.js
if %errorlevel% neq 0 echo WARNING: Dashboard generation failed

echo.
echo ========================================
echo    Test Suite Complete
echo ========================================
echo.

echo All tests completed at %date% %time%
echo.
echo Results are available in the 'results' directory:
dir /b results\*.html 2>nul | findstr /r ".*" >nul && (
    echo.
    echo HTML Reports:
    for %%f in (results\*.html) do echo   - %%f
)

dir /b results\*.json 2>nul | findstr /r ".*" >nul && (
    echo.
    echo JSON Reports:
    for %%f in (results\*.json) do echo   - %%f
)

echo.
echo Open 'results/dashboard.html' for a comprehensive overview
echo.

pause
