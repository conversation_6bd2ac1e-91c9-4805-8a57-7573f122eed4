// Dashboard Generator for API Performance Monitoring
// This script generates comprehensive HTML dashboards from test results

import { readFileSync, writeFileSync, readdirSync } from 'fs';
import { join, dirname } from 'path';

class DashboardGenerator {
  constructor(resultsDir = '../results') {
    this.resultsDir = resultsDir;
    this.testResults = [];
  }

  // Load all test result files
  loadTestResults() {
    try {
      const files = readdirSync(this.resultsDir);
      const jsonFiles = files.filter(f => f.endsWith('.json'));
      
      for (const file of jsonFiles) {
        try {
          const content = readFileSync(join(this.resultsDir, file), 'utf8');
          const data = JSON.parse(content);
          
          // Extract metadata from filename
          const metadata = this.parseFilename(file);
          
          this.testResults.push({
            filename: file,
            timestamp: metadata.timestamp,
            testType: metadata.testType,
            scenario: metadata.scenario,
            data: data
          });
        } catch (error) {
          console.warn(`Failed to load ${file}:`, error.message);
        }
      }
      
      // Sort by timestamp
      this.testResults.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
      
      console.log(`Loaded ${this.testResults.length} test results`);
    } catch (error) {
      console.error('Failed to load test results:', error);
    }
  }

  // Parse filename to extract metadata
  parseFilename(filename) {
    // Expected format: blur-detection-moderate-load-2024-01-15T10-30-00.json
    const parts = filename.replace('.json', '').split('-');
    
    let testType = 'unknown';
    let scenario = 'unknown';
    let timestamp = new Date().toISOString();
    
    if (parts.length >= 3) {
      if (parts[0] === 'blur' && parts[1] === 'detection') {
        testType = 'blur-detection';
        scenario = parts.slice(2, -1).join('-');
        timestamp = parts[parts.length - 1].replace(/-/g, ':');
      } else if (parts[0] === 'image' && parts[1] === 'inspector') {
        testType = 'image-inspector';
        scenario = parts.slice(2, -1).join('-');
        timestamp = parts[parts.length - 1].replace(/-/g, ':');
      } else if (parts[0] === 'monitoring') {
        testType = 'monitoring';
        scenario = 'continuous';
        timestamp = parts[parts.length - 1].replace(/-/g, ':');
      } else if (parts[0] === 'baseline') {
        testType = 'baseline';
        scenario = 'performance';
        timestamp = parts[parts.length - 1].replace(/-/g, ':');
      }
    }
    
    return { testType, scenario, timestamp };
  }

  // Generate comprehensive dashboard
  generateDashboard() {
    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>API Stress Testing Dashboard</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; background: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .header p { color: #7f8c8d; font-size: 1.1em; }
        
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .summary-card h3 { color: #2c3e50; margin-bottom: 15px; }
        .summary-value { font-size: 2.5em; font-weight: bold; margin-bottom: 10px; }
        .summary-label { color: #7f8c8d; font-size: 0.9em; }
        
        .success { color: #27ae60; }
        .warning { color: #f39c12; }
        .error { color: #e74c3c; }
        
        .section { background: white; margin-bottom: 30px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .section-header { background: #3498db; color: white; padding: 20px; }
        .section-content { padding: 25px; }
        
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        .test-card { border: 1px solid #e0e0e0; border-radius: 8px; padding: 20px; }
        .test-card h4 { color: #2c3e50; margin-bottom: 15px; }
        .test-metrics { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; }
        .metric { padding: 8px; background: #f8f9fa; border-radius: 4px; }
        .metric-label { font-size: 0.8em; color: #7f8c8d; }
        .metric-value { font-weight: bold; color: #2c3e50; }
        
        .timeline { margin: 20px 0; }
        .timeline-item { display: flex; align-items: center; padding: 15px; border-left: 4px solid #3498db; margin-bottom: 10px; background: #f8f9fa; }
        .timeline-time { font-weight: bold; color: #2c3e50; min-width: 150px; }
        .timeline-content { flex: 1; margin-left: 20px; }
        
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #e0e0e0; }
        th { background: #f8f9fa; font-weight: bold; color: #2c3e50; }
        tr:hover { background: #f8f9fa; }
        
        .chart-container { height: 300px; margin: 20px 0; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #7f8c8d; }
        
        .nav-tabs { display: flex; border-bottom: 2px solid #e0e0e0; margin-bottom: 20px; }
        .nav-tab { padding: 15px 25px; cursor: pointer; border-bottom: 2px solid transparent; transition: all 0.3s; }
        .nav-tab:hover { background: #f8f9fa; }
        .nav-tab.active { border-bottom-color: #3498db; color: #3498db; font-weight: bold; }
        
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        
        @media (max-width: 768px) {
            .summary-grid { grid-template-columns: 1fr; }
            .test-grid { grid-template-columns: 1fr; }
            .test-metrics { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 API Stress Testing Dashboard</h1>
            <p>Comprehensive performance analysis and monitoring results</p>
            <p>Generated: ${new Date().toISOString()}</p>
        </div>
        
        ${this.generateSummarySection()}
        ${this.generateTestResultsSection()}
        ${this.generateTimelineSection()}
        ${this.generateComparisonSection()}
    </div>
    
    <script>
        // Tab functionality
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
        
        // Initialize first tab as active
        document.addEventListener('DOMContentLoaded', function() {
            const firstTab = document.querySelector('.nav-tab');
            const firstContent = document.querySelector('.tab-content');
            if (firstTab && firstContent) {
                firstTab.classList.add('active');
                firstContent.classList.add('active');
            }
        });
        
        // Auto-refresh every 5 minutes
        setTimeout(() => location.reload(), 300000);
    </script>
</body>
</html>`;

    return html;
  }

  generateSummarySection() {
    const totalTests = this.testResults.length;
    const recentTests = this.testResults.filter(r => 
      new Date(r.timestamp) > new Date(Date.now() - 24 * 60 * 60 * 1000)
    );
    
    const avgSuccessRate = this.calculateAverageSuccessRate();
    const avgResponseTime = this.calculateAverageResponseTime();
    
    return `
        <div class="summary-grid">
            <div class="summary-card">
                <h3>Total Tests</h3>
                <div class="summary-value">${totalTests}</div>
                <div class="summary-label">All time</div>
            </div>
            <div class="summary-card">
                <h3>Recent Tests</h3>
                <div class="summary-value">${recentTests.length}</div>
                <div class="summary-label">Last 24 hours</div>
            </div>
            <div class="summary-card">
                <h3>Success Rate</h3>
                <div class="summary-value ${this.getStatusClass(avgSuccessRate, 95)}">${avgSuccessRate.toFixed(1)}%</div>
                <div class="summary-label">Average across all tests</div>
            </div>
            <div class="summary-card">
                <h3>Response Time</h3>
                <div class="summary-value ${this.getResponseTimeClass(avgResponseTime)}">${avgResponseTime.toFixed(0)}ms</div>
                <div class="summary-label">Average response time</div>
            </div>
        </div>`;
  }

  generateTestResultsSection() {
    const blurTests = this.testResults.filter(r => r.testType === 'blur-detection');
    const inspectorTests = this.testResults.filter(r => r.testType === 'image-inspector');
    const monitoringTests = this.testResults.filter(r => r.testType === 'monitoring');
    
    return `
        <div class="section">
            <div class="section-header">
                <h2>📊 Test Results Overview</h2>
            </div>
            <div class="section-content">
                <div class="nav-tabs">
                    <div class="nav-tab" onclick="showTab('blur-results')">Blur Detection API</div>
                    <div class="nav-tab" onclick="showTab('inspector-results')">Image Inspector API</div>
                    <div class="nav-tab" onclick="showTab('monitoring-results')">Monitoring</div>
                </div>
                
                <div id="blur-results" class="tab-content">
                    ${this.generateAPITestResults(blurTests, 'Blur Detection API')}
                </div>
                
                <div id="inspector-results" class="tab-content">
                    ${this.generateAPITestResults(inspectorTests, 'Image Inspector API')}
                </div>
                
                <div id="monitoring-results" class="tab-content">
                    ${this.generateMonitoringResults(monitoringTests)}
                </div>
            </div>
        </div>`;
  }

  generateAPITestResults(tests, apiName) {
    if (tests.length === 0) {
      return `<p>No test results available for ${apiName}</p>`;
    }
    
    return `
        <h3>${apiName} Results (${tests.length} tests)</h3>
        <div class="test-grid">
            ${tests.map(test => this.generateTestCard(test)).join('')}
        </div>`;
  }

  generateTestCard(test) {
    const metrics = test.data.metrics || {};
    const successRate = ((1 - (metrics.http_req_failed?.values?.rate || 0)) * 100);
    const avgResponseTime = metrics.http_req_duration?.values?.avg || 0;
    const totalRequests = metrics.http_reqs?.values?.count || 0;
    
    return `
        <div class="test-card">
            <h4>${test.scenario} - ${new Date(test.timestamp).toLocaleDateString()}</h4>
            <div class="test-metrics">
                <div class="metric">
                    <div class="metric-label">Success Rate</div>
                    <div class="metric-value ${this.getStatusClass(successRate, 95)}">${successRate.toFixed(1)}%</div>
                </div>
                <div class="metric">
                    <div class="metric-label">Avg Response Time</div>
                    <div class="metric-value">${avgResponseTime.toFixed(0)}ms</div>
                </div>
                <div class="metric">
                    <div class="metric-label">Total Requests</div>
                    <div class="metric-value">${totalRequests}</div>
                </div>
                <div class="metric">
                    <div class="metric-label">95th Percentile</div>
                    <div class="metric-value">${(metrics.http_req_duration?.values?.['p(95)'] || 0).toFixed(0)}ms</div>
                </div>
            </div>
        </div>`;
  }

  generateMonitoringResults(tests) {
    if (tests.length === 0) {
      return `<p>No monitoring results available</p>`;
    }
    
    return `
        <h3>Continuous Monitoring Results (${tests.length} reports)</h3>
        <div class="chart-container">
            <p>📈 Monitoring trends chart would be displayed here</p>
        </div>
        <table>
            <tr>
                <th>Timestamp</th>
                <th>Blur API Availability</th>
                <th>Inspector API Availability</th>
                <th>Avg Response Time</th>
            </tr>
            ${tests.slice(-10).map(test => {
              const metrics = test.data.metrics || {};
              return `
                <tr>
                    <td>${new Date(test.timestamp).toLocaleString()}</td>
                    <td class="${this.getStatusClass((metrics.blur_api_availability?.values?.rate || 0) * 100, 99)}">
                        ${((metrics.blur_api_availability?.values?.rate || 0) * 100).toFixed(1)}%
                    </td>
                    <td class="${this.getStatusClass((metrics.inspector_api_availability?.values?.rate || 0) * 100, 99)}">
                        ${((metrics.inspector_api_availability?.values?.rate || 0) * 100).toFixed(1)}%
                    </td>
                    <td>${((metrics.blur_api_response_time?.values?.avg || 0) + (metrics.inspector_api_response_time?.values?.avg || 0)) / 2}ms</td>
                </tr>`;
            }).join('')}
        </table>`;
  }

  generateTimelineSection() {
    const recentTests = this.testResults.slice(-10);
    
    return `
        <div class="section">
            <div class="section-header">
                <h2>⏱️ Recent Test Timeline</h2>
            </div>
            <div class="section-content">
                <div class="timeline">
                    ${recentTests.reverse().map(test => `
                        <div class="timeline-item">
                            <div class="timeline-time">${new Date(test.timestamp).toLocaleString()}</div>
                            <div class="timeline-content">
                                <strong>${test.testType}</strong> - ${test.scenario}
                                <br>
                                <small>
                                    ${(test.data.metrics?.http_reqs?.values?.count || 0)} requests, 
                                    ${((1 - (test.data.metrics?.http_req_failed?.values?.rate || 0)) * 100).toFixed(1)}% success rate
                                </small>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>`;
  }

  generateComparisonSection() {
    return `
        <div class="section">
            <div class="section-header">
                <h2>📈 Performance Comparison</h2>
            </div>
            <div class="section-content">
                <div class="chart-container">
                    <p>📊 Performance comparison charts would be displayed here</p>
                    <p>Compare response times, success rates, and throughput across different test scenarios</p>
                </div>
            </div>
        </div>`;
  }

  calculateAverageSuccessRate() {
    if (this.testResults.length === 0) return 0;
    
    const rates = this.testResults.map(test => {
      const failRate = test.data.metrics?.http_req_failed?.values?.rate || 0;
      return (1 - failRate) * 100;
    });
    
    return rates.reduce((sum, rate) => sum + rate, 0) / rates.length;
  }

  calculateAverageResponseTime() {
    if (this.testResults.length === 0) return 0;
    
    const times = this.testResults.map(test => 
      test.data.metrics?.http_req_duration?.values?.avg || 0
    );
    
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }

  getStatusClass(value, threshold) {
    if (value >= threshold) return 'success';
    if (value >= threshold * 0.9) return 'warning';
    return 'error';
  }

  getResponseTimeClass(time) {
    if (time < 1000) return 'success';
    if (time < 3000) return 'warning';
    return 'error';
  }

  // Save dashboard to file
  saveDashboard(filename = 'dashboard.html') {
    const html = this.generateDashboard();
    writeFileSync(join(this.resultsDir, filename), html);
    console.log(`Dashboard saved to ${filename}`);
  }
}

// Usage
const generator = new DashboardGenerator();
generator.loadTestResults();
generator.saveDashboard();

export default DashboardGenerator;
