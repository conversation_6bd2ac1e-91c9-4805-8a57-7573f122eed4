import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// Custom metrics
export const errorRate = new Rate('errors');
export const responseTime = new Trend('response_time');
export const requestCount = new Counter('requests');

// Load configuration files
export function loadConfig(configPath) {
  try {
    const config = JSON.parse(open(configPath));
    return config;
  } catch (error) {
    console.error(`Failed to load config from ${configPath}:`, error);
    return null;
  }
}

// Load test scenarios
export const scenarios = loadConfig('../../config/test-scenarios.json');
export const testImages = loadConfig('../../config/test-images.json');

// Random selection utilities
export function getRandomImage(category = 'medium_images') {
  const images = testImages.test_images[category];
  return images[Math.floor(Math.random() * images.length)];
}

export function getRandomPayload(service, template) {
  const payloadTemplate = testImages.payload_templates[service][template];
  const imageUrl = getRandomImage();
  
  // Replace placeholder with actual image URL
  const payload = JSON.parse(JSON.stringify(payloadTemplate).replace('{IMAGE_URL}', imageUrl));
  return payload;
}

// Response validation
export function validateResponse(response, expectedStatus = 200, serviceName = 'API') {
  const isSuccess = check(response, {
    [`${serviceName} status is ${expectedStatus}`]: (r) => r.status === expectedStatus,
    [`${serviceName} response time < 5s`]: (r) => r.timings.duration < 5000,
    [`${serviceName} response has body`]: (r) => r.body && r.body.length > 0,
  });

  // Record metrics
  errorRate.add(!isSuccess);
  responseTime.add(response.timings.duration);
  requestCount.add(1);

  // Log errors for debugging
  if (!isSuccess) {
    console.error(`${serviceName} Error - Status: ${response.status}, Body: ${response.body}`);
  }

  return isSuccess;
}

// Realistic user behavior simulation
export function simulateUserThinkTime(min = 1, max = 3) {
  const thinkTime = Math.random() * (max - min) + min;
  sleep(thinkTime);
}

// Load testing patterns
export function getLoadPattern(scenarioName) {
  const scenario = scenarios.scenarios[scenarioName];
  if (!scenario) {
    console.error(`Scenario ${scenarioName} not found`);
    return null;
  }
  return scenario;
}

// Generate test options for k6
export function generateTestOptions(scenarioName) {
  const scenario = getLoadPattern(scenarioName);
  if (!scenario) return {};

  const options = {
    thresholds: scenarios.thresholds,
    summaryTrendStats: scenarios.options.summaryTrendStats,
    summaryTimeUnit: scenarios.options.summaryTimeUnit,
  };

  if (scenario.stages) {
    // Multi-stage scenario
    options.stages = scenario.stages;
  } else {
    // Simple scenario
    options.stages = [
      { duration: scenario.ramp_up || '30s', target: scenario.virtual_users },
      { duration: scenario.duration, target: scenario.virtual_users },
      { duration: scenario.ramp_down || '30s', target: 0 },
    ];
  }

  return options;
}

// Performance monitoring
export function logPerformanceMetrics(response, operation) {
  const metrics = {
    operation: operation,
    status: response.status,
    duration: response.timings.duration,
    size: response.body ? response.body.length : 0,
    timestamp: new Date().toISOString()
  };
  
  console.log(`Performance: ${JSON.stringify(metrics)}`);
}

// Error handling and retry logic
export function withRetry(requestFunc, maxRetries = 3, delay = 1000) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = requestFunc();
      if (response.status < 500) {
        return response; // Success or client error (don't retry)
      }
      
      if (attempt === maxRetries) {
        return response; // Last attempt, return whatever we got
      }
      
      console.log(`Attempt ${attempt} failed with status ${response.status}, retrying...`);
      sleep(delay / 1000);
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
      console.log(`Attempt ${attempt} failed with error: ${error}, retrying...`);
      sleep(delay / 1000);
    }
  }
}

// Data generation utilities
export function generateVariedPayloads(service, count = 10) {
  const payloads = [];
  const templates = Object.keys(testImages.payload_templates[service]);
  const imageCategories = Object.keys(testImages.test_images);
  
  for (let i = 0; i < count; i++) {
    const template = templates[i % templates.length];
    const category = imageCategories[i % imageCategories.length];
    const imageUrl = getRandomImage(category);
    
    const payload = JSON.parse(
      JSON.stringify(testImages.payload_templates[service][template])
        .replace('{IMAGE_URL}', imageUrl)
    );
    
    payloads.push(payload);
  }
  
  return payloads;
}
