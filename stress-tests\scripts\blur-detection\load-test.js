import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { 
  getRandomPayload, 
  validateResponse, 
  simulateUserThinkTime, 
  generateTestOptions,
  withRetry,
  logPerformanceMetrics 
} from '../utils/common.js';

// Custom metrics for Blur Detection API
const blurDetectionErrors = new Rate('blur_detection_errors');
const blurDetectionDuration = new Trend('blur_detection_duration');
const blurDetectionRequests = new Counter('blur_detection_requests');
const imageProcessingTime = new Trend('image_processing_time');

// Configuration
const BASE_URL = 'http://localhost:8000';
const SCENARIO = __ENV.SCENARIO || 'moderate_load';

// Test options based on scenario
export let options = generateTestOptions(SCENARIO);

// Add specific thresholds for blur detection
options.thresholds = {
  ...options.thresholds,
  'blur_detection_errors': ['rate<0.05'], // Less than 5% errors
  'blur_detection_duration': ['p(95)<3000'], // 95% under 3 seconds
  'image_processing_time': ['p(90)<2000'], // 90% processing under 2 seconds
};

export default function () {
  const testScenarios = [
    testHealthEndpoints,
    testBlurDetectionBasic,
    testBlurDetectionVariedSizes,
    testBlurDetectionHighResolution,
    testBlurDetectionBatch,
    testErrorHandling
  ];
  
  // Randomly select test scenarios to simulate realistic user behavior
  const scenario = testScenarios[Math.floor(Math.random() * testScenarios.length)];
  scenario();
  
  simulateUserThinkTime(1, 4);
}

function testHealthEndpoints() {
  // Health check
  let response = http.get(`${BASE_URL}/health`);
  validateResponse(response, 200, 'Health Check');
  
  sleep(0.5);
  
  // Root endpoint
  response = http.get(`${BASE_URL}/`);
  validateResponse(response, 200, 'Root Endpoint');
  
  sleep(0.5);
  
  // Thresholds endpoint
  response = http.get(`${BASE_URL}/thresholds`);
  validateResponse(response, 200, 'Thresholds');
}

function testBlurDetectionBasic() {
  const payload = getRandomPayload('blur_detection', 'basic');
  
  const response = withRetry(() => 
    http.post(`${BASE_URL}/detect-blur`, JSON.stringify(payload), {
      headers: { 'Content-Type': 'application/json' },
      timeout: '30s',
    })
  );
  
  const isSuccess = check(response, {
    'Blur detection status 200': (r) => r.status === 200,
    'Blur detection response time < 5s': (r) => r.timings.duration < 5000,
    'Blur detection has laplacian_variance': (r) => {
      try {
        const body = JSON.parse(r.body);
        return typeof body.laplacian_variance === 'number';
      } catch (e) {
        return false;
      }
    },
    'Blur detection has blur_level': (r) => {
      try {
        const body = JSON.parse(r.body);
        return ['sharp', 'slightly_blurred', 'moderately_blurred', 'heavily_blurred'].includes(body.blur_level);
      } catch (e) {
        return false;
      }
    },
    'Blur detection has processing_time': (r) => {
      try {
        const body = JSON.parse(r.body);
        return typeof body.processing_time_ms === 'number' && body.processing_time_ms > 0;
      } catch (e) {
        return false;
      }
    },
  });
  
  // Record custom metrics
  blurDetectionErrors.add(!isSuccess);
  blurDetectionDuration.add(response.timings.duration);
  blurDetectionRequests.add(1);
  
  if (isSuccess && response.body) {
    try {
      const result = JSON.parse(response.body);
      imageProcessingTime.add(result.processing_time_ms);
      logPerformanceMetrics(response, `blur_detection_basic_${result.blur_level}`);
    } catch (e) {
      console.error('Failed to parse response body:', e);
    }
  }
}

function testBlurDetectionVariedSizes() {
  const sizeTemplates = ['basic', 'high_res', 'low_res'];
  const template = sizeTemplates[Math.floor(Math.random() * sizeTemplates.length)];
  const payload = getRandomPayload('blur_detection', template);
  
  const response = http.post(`${BASE_URL}/detect-blur`, JSON.stringify(payload), {
    headers: { 'Content-Type': 'application/json' },
    timeout: '45s', // Longer timeout for high-res images
  });
  
  const isSuccess = validateResponse(response, 200, `Blur Detection ${template}`);
  
  if (isSuccess && response.body) {
    try {
      const result = JSON.parse(response.body);
      console.log(`${template} - Variance: ${result.laplacian_variance}, Processing: ${result.processing_time_ms}ms`);
    } catch (e) {
      console.error('Failed to parse varied size response:', e);
    }
  }
}

function testBlurDetectionHighResolution() {
  const payload = getRandomPayload('blur_detection', 'high_res');
  
  const response = http.post(`${BASE_URL}/detect-blur`, JSON.stringify(payload), {
    headers: { 'Content-Type': 'application/json' },
    timeout: '60s', // Extended timeout for high-resolution processing
  });
  
  const isSuccess = check(response, {
    'High-res blur detection status': (r) => r.status === 200,
    'High-res processing time reasonable': (r) => r.timings.duration < 30000, // 30s max
    'High-res has downscaling info': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.image_info && typeof body.image_info.was_downscaled === 'boolean';
      } catch (e) {
        return false;
      }
    },
  });
  
  blurDetectionErrors.add(!isSuccess);
  blurDetectionRequests.add(1);
}

function testBlurDetectionBatch() {
  // Simulate batch processing by making multiple concurrent requests
  const batchSize = Math.floor(Math.random() * 3) + 2; // 2-4 requests
  const requests = [];
  
  for (let i = 0; i < batchSize; i++) {
    const payload = getRandomPayload('blur_detection', 'basic');
    requests.push([
      'POST',
      `${BASE_URL}/detect-blur`,
      JSON.stringify(payload),
      { headers: { 'Content-Type': 'application/json' } }
    ]);
  }
  
  const responses = http.batch(requests);
  
  responses.forEach((response, index) => {
    const isSuccess = check(response, {
      [`Batch request ${index + 1} status`]: (r) => r.status === 200,
      [`Batch request ${index + 1} response time`]: (r) => r.timings.duration < 10000,
    });
    
    blurDetectionErrors.add(!isSuccess);
    blurDetectionRequests.add(1);
  });
  
  console.log(`Batch processing: ${batchSize} requests completed`);
}

function testErrorHandling() {
  // Test with invalid image URL
  const invalidPayload = {
    image_url: "https://invalid-url-that-does-not-exist.com/image.jpg",
    max_resolution: 1024
  };
  
  let response = http.post(`${BASE_URL}/detect-blur`, JSON.stringify(invalidPayload), {
    headers: { 'Content-Type': 'application/json' },
    timeout: '15s',
  });
  
  check(response, {
    'Invalid URL handled gracefully': (r) => r.status >= 400 && r.status < 600,
    'Error response has detail': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.detail !== undefined;
      } catch (e) {
        return r.body.includes('error') || r.body.includes('failed');
      }
    },
  });
  
  sleep(1);
  
  // Test with malformed JSON
  response = http.post(`${BASE_URL}/detect-blur`, '{"invalid": json}', {
    headers: { 'Content-Type': 'application/json' },
  });
  
  check(response, {
    'Malformed JSON handled': (r) => r.status === 422 || r.status === 400,
  });
  
  sleep(1);
  
  // Test with missing required fields
  response = http.post(`${BASE_URL}/detect-blur`, '{}', {
    headers: { 'Content-Type': 'application/json' },
  });
  
  check(response, {
    'Missing fields validation': (r) => r.status === 422,
  });
}

export function handleSummary(data) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const scenarioName = SCENARIO.replace('_', '-');
  
  return {
    [`../../results/blur-detection-${scenarioName}-${timestamp}.json`]: JSON.stringify(data, null, 2),
    [`../../results/blur-detection-${scenarioName}-${timestamp}.html`]: htmlReport(data),
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
  };
}

function htmlReport(data) {
  const metrics = data.metrics;
  const timestamp = new Date().toISOString();
  
  return `
<!DOCTYPE html>
<html>
<head>
    <title>Blur Detection API - Load Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .header { text-align: center; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 20px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .metric-title { font-weight: bold; color: #495057; margin-bottom: 10px; }
        .metric-value { font-size: 1.2em; color: #007bff; }
        .success { border-left-color: #28a745; }
        .warning { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #dee2e6; padding: 12px; text-align: left; }
        th { background-color: #e9ecef; font-weight: bold; }
        .summary { background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Blur Detection API Load Test Report</h1>
            <p>Scenario: <strong>${SCENARIO}</strong> | Generated: ${timestamp}</p>
        </div>
        
        <div class="summary">
            <h2>📊 Test Summary</h2>
            <div class="metric-grid">
                <div class="metric-card ${(metrics.http_req_failed?.values?.rate || 0) < 0.05 ? 'success' : 'error'}">
                    <div class="metric-title">Success Rate</div>
                    <div class="metric-value">${((1 - (metrics.http_req_failed?.values?.rate || 0)) * 100).toFixed(2)}%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Total Requests</div>
                    <div class="metric-value">${metrics.http_reqs?.values?.count || 0}</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Avg Response Time</div>
                    <div class="metric-value">${(metrics.http_req_duration?.values?.avg || 0).toFixed(2)}ms</div>
                </div>
                <div class="metric-card ${(metrics.http_req_duration?.values?.['p(95)'] || 0) < 3000 ? 'success' : 'warning'}">
                    <div class="metric-title">95th Percentile</div>
                    <div class="metric-value">${(metrics.http_req_duration?.values?.['p(95)'] || 0).toFixed(2)}ms</div>
                </div>
            </div>
        </div>
        
        <h2>🎯 Performance Metrics</h2>
        <table>
            <tr><th>Metric</th><th>Average</th><th>Min</th><th>Max</th><th>90th %ile</th><th>95th %ile</th></tr>
            <tr>
                <td>HTTP Request Duration</td>
                <td>${(metrics.http_req_duration?.values?.avg || 0).toFixed(2)}ms</td>
                <td>${(metrics.http_req_duration?.values?.min || 0).toFixed(2)}ms</td>
                <td>${(metrics.http_req_duration?.values?.max || 0).toFixed(2)}ms</td>
                <td>${(metrics.http_req_duration?.values?.['p(90)'] || 0).toFixed(2)}ms</td>
                <td>${(metrics.http_req_duration?.values?.['p(95)'] || 0).toFixed(2)}ms</td>
            </tr>
            <tr>
                <td>Blur Detection Duration</td>
                <td>${(metrics.blur_detection_duration?.values?.avg || 0).toFixed(2)}ms</td>
                <td>${(metrics.blur_detection_duration?.values?.min || 0).toFixed(2)}ms</td>
                <td>${(metrics.blur_detection_duration?.values?.max || 0).toFixed(2)}ms</td>
                <td>${(metrics.blur_detection_duration?.values?.['p(90)'] || 0).toFixed(2)}ms</td>
                <td>${(metrics.blur_detection_duration?.values?.['p(95)'] || 0).toFixed(2)}ms</td>
            </tr>
            <tr>
                <td>Image Processing Time</td>
                <td>${(metrics.image_processing_time?.values?.avg || 0).toFixed(2)}ms</td>
                <td>${(metrics.image_processing_time?.values?.min || 0).toFixed(2)}ms</td>
                <td>${(metrics.image_processing_time?.values?.max || 0).toFixed(2)}ms</td>
                <td>${(metrics.image_processing_time?.values?.['p(90)'] || 0).toFixed(2)}ms</td>
                <td>${(metrics.image_processing_time?.values?.['p(95)'] || 0).toFixed(2)}ms</td>
            </tr>
        </table>
        
        <h2>📈 Custom Metrics</h2>
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-title">Blur Detection Requests</div>
                <div class="metric-value">${metrics.blur_detection_requests?.values?.count || 0}</div>
            </div>
            <div class="metric-card ${(metrics.blur_detection_errors?.values?.rate || 0) < 0.05 ? 'success' : 'error'}">
                <div class="metric-title">Blur Detection Error Rate</div>
                <div class="metric-value">${((metrics.blur_detection_errors?.values?.rate || 0) * 100).toFixed(2)}%</div>
            </div>
        </div>
    </div>
</body>
</html>`;
}
