{"scenarios": {"light_load": {"name": "Light Load Test", "description": "Normal expected traffic simulation", "virtual_users": 10, "duration": "2m", "ramp_up": "30s", "ramp_down": "30s"}, "moderate_load": {"name": "Moderate Load Test", "description": "Higher than normal traffic", "virtual_users": 50, "duration": "5m", "ramp_up": "1m", "ramp_down": "1m"}, "heavy_load": {"name": "Heavy Load Test", "description": "Peak traffic simulation", "virtual_users": 100, "duration": "10m", "ramp_up": "2m", "ramp_down": "2m"}, "spike_test": {"name": "Spike Test", "description": "Sudden traffic spike simulation", "stages": [{"duration": "1m", "target": 10}, {"duration": "30s", "target": 200}, {"duration": "2m", "target": 200}, {"duration": "30s", "target": 10}, {"duration": "1m", "target": 10}]}, "soak_test": {"name": "Soak Test", "description": "Extended endurance testing", "virtual_users": 25, "duration": "30m", "ramp_up": "2m", "ramp_down": "2m"}, "stress_test": {"name": "Stress Test", "description": "Beyond normal capacity testing", "stages": [{"duration": "2m", "target": 50}, {"duration": "3m", "target": 100}, {"duration": "3m", "target": 150}, {"duration": "3m", "target": 200}, {"duration": "2m", "target": 0}]}}, "thresholds": {"http_req_duration": ["p(95)<2000", "p(99)<5000"], "http_req_failed": ["rate<0.05"], "http_reqs": ["rate>10"]}, "options": {"summaryTrendStats": ["avg", "min", "med", "max", "p(90)", "p(95)", "p(99)"], "summaryTimeUnit": "ms"}}