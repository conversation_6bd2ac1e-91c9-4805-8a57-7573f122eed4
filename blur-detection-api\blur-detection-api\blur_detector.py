import cv2
import numpy as np
import aiohttp
import asyncio
from PIL import Image
import io
import time
from typing import <PERSON>ple, Optional
from models import BlurLevel, ImageInfo

class BlurDetector:
    def __init__(self):
        # Thresholds for blur classification based on Laplacian variance
        self.thresholds = {
            "sharp": 500,
            "slightly_blurred": 200,
            "moderately_blurred": 50,
            "heavily_blurred": 0
        }
    
    async def download_image(self, url: str, timeout: int = 10) -> Tuple[bytes, int]:
        """Download image from URL and return bytes with size"""
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=timeout)) as response:
                if response.status != 200:
                    raise Exception(f"Failed to download image: HTTP {response.status}")
                
                image_data = await response.read()
                return image_data, len(image_data)
    
    def preprocess_image(self, image_data: bytes, max_resolution: int = 1024) -> <PERSON><PERSON>[np.n<PERSON><PERSON>, ImageInfo, bool]:
        """Preprocess image: load, downscale if needed, convert to grayscale"""
        # Load image with PIL first to get metadata
        pil_image = Image.open(io.BytesIO(image_data))
        original_size = len(image_data)
        original_dims = pil_image.size  # (width, height)
        
        # Convert to RGB if needed
        if pil_image.mode in ('RGBA', 'LA', 'P'):
            pil_image = pil_image.convert('RGB')
        
        was_downscaled = False
        
        # Downscale if image is > 1MB or dimensions exceed max_resolution
        if original_size > 1024 * 1024 or max(original_dims) > max_resolution:
            # Calculate new dimensions maintaining aspect ratio
            width, height = original_dims
            if width > height:
                new_width = min(max_resolution, width)
                new_height = int(height * (new_width / width))
            else:
                new_height = min(max_resolution, height)
                new_width = int(width * (new_height / height))
            
            pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            was_downscaled = True
        
        # Convert PIL to OpenCV format
        cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
        # Convert to grayscale for blur detection
        gray_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
        
        image_info = ImageInfo(
            original_size_bytes=original_size,
            original_dimensions=original_dims,
            processed_dimensions=gray_image.shape[::-1],  # (width, height)
            was_downscaled=was_downscaled,
            format=pil_image.format or "Unknown",
            mode=pil_image.mode
        )
        
        return gray_image, image_info, was_downscaled
    
    def calculate_laplacian_variance(self, gray_image: np.ndarray) -> float:
        """Calculate Laplacian variance for blur detection"""
        # Apply Laplacian operator
        laplacian = cv2.Laplacian(gray_image, cv2.CV_64F)
        
        # Calculate variance
        variance = laplacian.var()
        
        return float(variance)
    
    def classify_blur(self, laplacian_variance: float) -> Tuple[BlurLevel, bool, float]:
        """Classify blur level based on Laplacian variance"""
        if laplacian_variance >= self.thresholds["sharp"]:
            return BlurLevel.SHARP, False, min(1.0, laplacian_variance / 1000)
        elif laplacian_variance >= self.thresholds["slightly_blurred"]:
            return BlurLevel.SLIGHTLY_BLURRED, True, 0.7
        elif laplacian_variance >= self.thresholds["moderately_blurred"]:
            return BlurLevel.MODERATELY_BLURRED, True, 0.5
        else:
            return BlurLevel.HEAVILY_BLURRED, True, 0.3
    
    def detect_overexposure(self, cv_image: np.ndarray) -> bool:
        """Detect if image is overexposed by analyzing histogram"""
        # Convert to grayscale for luminance analysis
        gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
        
        # Calculate histogram
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        
        # Check percentage of pixels in bright range (240-255)
        total_pixels = gray.shape[0] * gray.shape[1]
        bright_pixels = np.sum(hist[240:256])
        bright_percentage = (bright_pixels / total_pixels) * 100
        
        # Consider overexposed if more than 5% of pixels are in the brightest range
        return bright_percentage > 5.0
    
    def detect_oversaturation(self, cv_image: np.ndarray) -> bool:
        """Detect if image is oversaturated by analyzing color saturation"""
        # Convert BGR to HSV
        hsv = cv2.cvtColor(cv_image, cv2.COLOR_BGR2HSV)
        
        # Extract saturation channel
        saturation = hsv[:, :, 1]
        
        # Calculate histogram for saturation
        hist = cv2.calcHist([saturation], [0], None, [256], [0, 256])
        
        # Check percentage of pixels with high saturation (200-255)
        total_pixels = saturation.shape[0] * saturation.shape[1]
        high_sat_pixels = np.sum(hist[200:256])
        high_sat_percentage = (high_sat_pixels / total_pixels) * 100
        
        # Consider oversaturated if more than 15% of pixels have high saturation
        return high_sat_percentage > 15.0
    
    def detect_incorrect_white_balance(self, cv_image: np.ndarray) -> bool:
        """Detect incorrect white balance by analyzing color temperature"""
        # Calculate mean values for each color channel
        b_mean = np.mean(cv_image[:, :, 0])  # Blue
        g_mean = np.mean(cv_image[:, :, 1])  # Green
        r_mean = np.mean(cv_image[:, :, 2])  # Red
        
        # Calculate ratios
        if g_mean > 0:
            r_g_ratio = r_mean / g_mean
            b_g_ratio = b_mean / g_mean
        else:
            return False
        
        # Thresholds for color cast detection
        # Normal white balance should have ratios close to 1.0
        # Significant deviation indicates color cast
        r_threshold = 0.3  # Red cast threshold
        b_threshold = 0.3  # Blue cast threshold
        
        # Check for strong color casts
        has_red_cast = abs(r_g_ratio - 1.0) > r_threshold and r_g_ratio > 1.0
        has_blue_cast = abs(b_g_ratio - 1.0) > b_threshold and b_g_ratio > 1.0
        
        return has_red_cast or has_blue_cast
    
    async def detect_blur(self, image_url: str, max_resolution: int = 1024) -> dict:
        """Main blur detection function"""
        start_time = time.time()
        errors = []
        warnings = []
        
        try:
            # Download image
            image_data, file_size = await self.download_image(image_url)
            
            if file_size > 20 * 1024 * 1024:  # 20MB limit
                warnings.append(f"Large image size: {file_size / (1024*1024):.1f}MB")
            
            # Preprocess image
            gray_image, image_info, was_downscaled = self.preprocess_image(image_data, max_resolution)
            
            if was_downscaled:
                warnings.append("Image was downscaled for faster processing")
            
            # Convert PIL to OpenCV format for quality checks
            pil_image = Image.open(io.BytesIO(image_data))
            if pil_image.mode in ('RGBA', 'LA', 'P'):
                pil_image = pil_image.convert('RGB')
            cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            # Calculate Laplacian variance
            laplacian_variance = self.calculate_laplacian_variance(gray_image)
            
            # Classify blur
            blur_level, is_blurred, confidence = self.classify_blur(laplacian_variance)
            
            # Perform quality checks
            overexposed = self.detect_overexposure(cv_image)
            oversaturated = self.detect_oversaturation(cv_image)
            incorrect_wb = self.detect_incorrect_white_balance(cv_image)
            
            processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            return {
                "laplacian_variance": laplacian_variance,
                "blur_level": blur_level,
                "is_blurred": is_blurred,
                "confidence_score": confidence,
                "processing_time_ms": processing_time,
                "image_info": image_info.dict(),
                "overexposed": overexposed,
                "oversaturated": oversaturated,
                "incorrect_wb": incorrect_wb,
                "errors": errors if errors else None,
                "warnings": warnings if warnings else None
            }
            
        except Exception as e:
            errors.append(str(e))
            processing_time = (time.time() - start_time) * 1000
            
            return {
                "laplacian_variance": 0.0,
                "blur_level": BlurLevel.HEAVILY_BLURRED,
                "is_blurred": True,
                "confidence_score": 0.0,
                "processing_time_ms": processing_time,
                "image_info": {},
                "overexposed": False,
                "oversaturated": False,
                "incorrect_wb": False,
                "errors": errors,
                "warnings": warnings
            }
